import asyncio
import inspect
from collections.abc import Awaitable, Callable
from typing import Any
from uuid import UUID

from pydantic import BaseModel

from app.agentic.context.schemas import (
    GetAccount,
    GetOpportunity,
    ListOpportunitiesByAccount,
    ToolDefinition,
    UpdateOpportunity,
)
from app.workspace.integrations.user_integrations import UserIntegrations


def get_tools(
    user_id: UUID,
    user_integrations: UserIntegrations,
) -> list[ToolDefinition]:
    tools: list[ToolDefinition] = []

    crm = user_integrations.crm()

    if not crm:
        raise RuntimeError(f"No CRM integration configured for user {user_id}")

    crm_methods: list[tuple[str, str, type[BaseModel]]] = [
        ("get_opportunity", "Fetch a CRM opportunity by its ID", GetOpportunity),
        (
            "update_opportunity",
            "Update a CRM opportunity with provided fields",
            UpdateOpportunity,
        ),
        (
            "list_opportunities_by_account",
            "List CRM opportunities for a given account",
            ListOpportunitiesByAccount,
        ),
        ("get_account", "Fetch a CRM account by its ID", GetAccount),
    ]

    for name, description, schema in crm_methods:
        method = getattr(crm, name)
        coroutine = _ensure_async(method)
        tools.append(
            ToolDefinition(
                name=name,
                coroutine=coroutine,
                description=description,
                args_schema=schema,
            )
        )

    return tools


def _ensure_async(fn: Callable[..., Any]) -> Callable[..., Awaitable[Any]]:
    if inspect.iscoroutinefunction(fn):
        return fn

    async def wrapper(*args: Any, **kwargs: Any) -> Any:
        return await asyncio.to_thread(fn, *args, **kwargs)

    return wrapper
