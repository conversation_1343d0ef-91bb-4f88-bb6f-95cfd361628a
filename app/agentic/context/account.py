from uuid import UUID

from app.workspace.integrations.user_integrations import UserIntegrations


def get_account_details(
    user_id: UUID,
    crm_account_id: str,
    user_integrations: UserIntegrations,
):
    crm_provider = user_integrations.crm()

    if not crm_provider:
        raise RuntimeError(f"No CRM integration configured for user {user_id}")

    account_details = crm_provider.get_account(crm_account_id)

    return account_details
