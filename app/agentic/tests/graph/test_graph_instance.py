import json
import uuid
from unittest.mock import AsyncMock

import pytest
from langchain_core.messages import AIMessage, AIMessageChunk, HumanMessage, ToolMessage
from langgraph.checkpoint.postgres import CheckpointTuple
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.graph.state import CompiledStateGraph, StateGraph
from langgraph.types import Command, Interrupt

from app.agentic.graph.graph_instance import GraphInstance, SseEventType


@pytest.fixture
def mock_checkpointer(mocker):
    return mocker.Mock(spec=AsyncPostgresSaver)


@pytest.fixture
def mock_compiled_graph(mocker):
    mock = mocker.AsyncMock(spec=CompiledStateGraph)
    mock.ainvoke = AsyncMock(return_value={"messages": ["mocked output"]})
    return mock


@pytest.fixture
def mock_graph_definition(mocker):
    mock = mocker.Mock(spec=StateGraph)
    mock.compile = mocker.Mock()
    return mock


@pytest.fixture
def graph_instance(
    mock_checkpointer,
    mock_graph_definition,
    mock_compiled_graph,
    mocker,
):
    mock_graph_definition.compile.return_value = mock_compiled_graph

    mock_saver_instance = mocker.AsyncMock(spec=AsyncPostgresSaver)
    mocker.patch(
        "app.agentic.graph.graph_instance.AsyncPostgresSaver",
        return_value=mock_saver_instance,
    )

    runner = GraphInstance(
        checkpointer=mock_checkpointer,
        graph_definition=mock_graph_definition,
    )

    assert runner.graph is mock_compiled_graph

    return runner


@pytest.mark.anyio
async def test_stream_graph(org_id, graph_instance, mocker):
    thread_id = str(uuid.uuid4())
    crm_account_id = "test-crm-account-id"

    graph_input = {
        "messages": ["stream input"],
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
    }

    async def mock_event_stream_instance(*_, **__):
        yield {
            "event": "on_chat_model_stream",
            "run_id": str(uuid.uuid4()),
            "name": "ChatModel",
            "tags": [],
            "metadata": {},
            "data": {"chunk": AIMessageChunk(content="Hello ")},
        }
        yield {
            "event": "on_chat_model_stream",
            "run_id": str(uuid.uuid4()),
            "name": "ChatModel",
            "tags": [],
            "metadata": {},
            "data": {"chunk": AIMessageChunk(content="World!")},
        }
        yield {
            "event": "on_chat_model_stream",
            "run_id": str(uuid.uuid4()),
            "name": "ChatModel",
            "tags": [],
            "metadata": {},
            "data": {
                "chunk": AIMessageChunk(content=[{"type": "text", "text": "..."}])
            },
        }

    patched_astream_events = mocker.patch.object(
        graph_instance.graph, "astream_events", side_effect=mock_event_stream_instance
    )

    stream_iterator = graph_instance.stream_graph(graph_input)
    results = [chunk async for chunk in stream_iterator]

    expected_results = [
        f"0:{json.dumps('Hello ')}\n",
        f"0:{json.dumps('World!')}\n",
    ]
    assert results == expected_results

    patched_astream_events.assert_called_once()
    call_args, call_kwargs = patched_astream_events.call_args

    assert call_args[0] == graph_input

    assert "config" in call_kwargs
    config = call_kwargs["config"]
    assert config["configurable"]["thread_id"] == thread_id
    assert config["configurable"]["checkpoint_ns"] == ""

    assert "version" in call_kwargs
    assert call_kwargs["version"] == "v2"


def test_format_sse_event_with_tool_message_and_tool_call_id():
    tool_message = ToolMessage(
        name="test_tool",
        content="Tool result content",
        tool_call_id="test_tool_call_123",
    )

    formatted_event = GraphInstance._format_sse_event(
        data=tool_message, event_type=SseEventType.TOOL_END
    )

    assert formatted_event.startswith("a:")

    json_data = json.loads(formatted_event[2:].replace("\n", ""))

    assert json_data == {
        "toolName": "test_tool",
        "args": "Tool result content",
        "toolCallId": "test_tool_call_123",
    }


def test_format_sse_event_with_tool_message_with_empty_tool_call_id():
    tool_message = ToolMessage(
        name="test_tool",
        content="Tool result content",
        tool_call_id="",
    )

    formatted_event = GraphInstance._format_sse_event(
        data=tool_message, event_type=SseEventType.TOOL_END
    )

    assert formatted_event.startswith("a:")

    json_data = json.loads(formatted_event[2:].replace("\n", ""))

    assert json_data == {
        "toolName": "test_tool",
        "args": "Tool result content",
    }
    assert "toolCallId" not in json_data


def test_format_sse_event_with_interrupt():
    interrupt = Interrupt(value="test_interrupt", resumable=True, ns="test_ns")

    formatted_event = GraphInstance._format_sse_event(
        data=interrupt, event_type=SseEventType.INTERRUPT
    )

    assert formatted_event.startswith("2:")
    json_data = json.loads(formatted_event[2:].replace("\n", ""))

    assert json_data == [
        {
            "value": "test_interrupt",
            "resumable": True,
            "ns": "test_ns",
        }
    ]


@pytest.mark.anyio
async def test_stream_graph_with_resume(org_id, graph_instance, mocker):
    thread_id = str(uuid.uuid4())
    crm_account_id = "test-crm-account-id"
    resume_id = "test-resume-id"

    graph_input = {
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
        "resume": resume_id,
    }

    async def mock_event_stream_instance(*_, **__):
        yield {
            "event": "on_chat_model_stream",
            "run_id": str(uuid.uuid4()),
            "name": "ChatModel",
            "tags": [],
            "metadata": {},
            "data": {"chunk": AIMessageChunk(content="Resumed ")},
        }
        yield {
            "event": "on_chat_model_stream",
            "run_id": str(uuid.uuid4()),
            "name": "ChatModel",
            "tags": [],
            "metadata": {},
            "data": {"chunk": AIMessageChunk(content="conversation!")},
        }

    patched_astream_events = mocker.patch.object(
        graph_instance.graph, "astream_events", side_effect=mock_event_stream_instance
    )

    stream_iterator = graph_instance.stream_graph(graph_input)
    results = [chunk async for chunk in stream_iterator]

    expected_results = [
        f"0:{json.dumps('Resumed ')}\n",
        f"0:{json.dumps('conversation!')}\n",
    ]
    assert results == expected_results

    patched_astream_events.assert_called_once()
    call_args, call_kwargs = patched_astream_events.call_args

    assert isinstance(call_args[0], Command)
    assert call_args[0].resume == resume_id

    assert "config" in call_kwargs
    config = call_kwargs["config"]
    assert config["configurable"]["thread_id"] == thread_id
    assert config["configurable"]["checkpoint_ns"] == ""

    assert "version" in call_kwargs
    assert call_kwargs["version"] == "v2"


@pytest.mark.anyio
async def test_stream_graph_with_on_chain_stream_command(
    org_id, graph_instance, mocker
):
    thread_id = str(uuid.uuid4())
    crm_account_id = "test-crm-account-id"

    graph_input = {
        "messages": ["stream input"],
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
    }

    async def mock_event_stream_instance(*_, **__):
        yield {
            "event": "on_chain_stream",
            "run_id": str(uuid.uuid4()),
            "name": "ChainNode",
            "tags": [],
            "metadata": {},
            "data": {"chunk": Command(update="Command update message")},
        }

    patched_astream_events = mocker.patch.object(
        graph_instance.graph, "astream_events", side_effect=mock_event_stream_instance
    )

    stream_iterator = graph_instance.stream_graph(graph_input)
    results = [chunk async for chunk in stream_iterator]

    expected_results = [
        '0:"Command update message"\n',
    ]
    assert results == expected_results

    patched_astream_events.assert_called_once()


@pytest.mark.anyio
async def test_stream_graph_with_on_chain_stream_interrupt(
    org_id, graph_instance, mocker
):
    thread_id = str(uuid.uuid4())
    crm_account_id = "test-crm-account-id"

    graph_input = {
        "messages": ["stream input"],
        "thread_id": thread_id,
        "crm_account_id": crm_account_id,
        "org_id": org_id,
    }

    interrupt = Interrupt(value="test_interrupt", resumable=True, ns="test_ns")

    async def mock_event_stream_instance(*_, **__):
        yield {
            "event": "on_chain_stream",
            "run_id": str(uuid.uuid4()),
            "name": "ChainNode",
            "tags": [],
            "metadata": {},
            "data": {"chunk": {"__interrupt__": (interrupt, "Interrupted")}},
        }

    patched_astream_events = mocker.patch.object(
        graph_instance.graph, "astream_events", side_effect=mock_event_stream_instance
    )

    stream_iterator = graph_instance.stream_graph(graph_input)
    results = [chunk async for chunk in stream_iterator]

    expected_results = [
        f"2:{json.dumps([{'value': 'test_interrupt', 'resumable': True, 'ns': 'test_ns'}])}\n",
    ]
    assert results == expected_results

    patched_astream_events.assert_called_once()


def test_parse_historical_message_data_with_ai_message(graph_instance):
    ai_message = AIMessage(id="1234", content="AI response")

    result = graph_instance._parse_historical_message_data(ai_message)

    assert result == {
        "id": "1234",
        "role": "assistant",
        "content": {"type": "text", "text": "AI response"},
    }


def test_parse_historical_message_data_with_human_message(graph_instance):
    human_message = HumanMessage(id="5678", content="Human question")

    result = graph_instance._parse_historical_message_data(human_message)

    assert result == {
        "id": "5678",
        "role": "user",
        "content": {"type": "text", "text": "Human question"},
    }


def test_get_paginated_messages(graph_instance):
    messages = [HumanMessage(content=f"Message {i}") for i in range(10)]

    page1 = graph_instance._get_paginated_messages(messages, page=1, size=3)
    assert len(page1) == 3
    assert page1[0].content == "Message 0"
    assert page1[2].content == "Message 2"

    page2 = graph_instance._get_paginated_messages(messages, page=2, size=3)
    assert len(page2) == 3
    assert page2[0].content == "Message 3"
    assert page2[2].content == "Message 5"

    page4 = graph_instance._get_paginated_messages(messages, page=4, size=3)
    assert len(page4) == 1
    assert page4[0].content == "Message 9"


@pytest.mark.anyio
async def test_get_latest_checkpoint_messages(mocker, graph_instance):
    thread_id = "test-thread-id"

    mock_messages = [
        HumanMessage(content="Test question"),
        AIMessage(content="Test response"),
    ]

    mock_checkpoint = mocker.Mock(spec=CheckpointTuple)
    mock_checkpoint.checkpoint = {"channel_values": {"messages": mock_messages}}

    async def mock_get_messages(_thread_id):
        return mock_messages

    mocker.patch.object(
        graph_instance, "_get_latest_checkpoint_messages", side_effect=mock_get_messages
    )

    result = await graph_instance._get_latest_checkpoint_messages(thread_id)

    assert result == mock_messages


@pytest.mark.anyio
async def test_get_latest_checkpoint_messages_no_checkpoint(graph_instance, mocker):
    thread_id = "test-thread-id"

    async def mock_get_no_messages(_thread_id):
        return None

    mocker.patch.object(
        graph_instance,
        "_get_latest_checkpoint_messages",
        side_effect=mock_get_no_messages,
    )

    result = await graph_instance._get_latest_checkpoint_messages(thread_id)

    assert result is None


@pytest.mark.anyio
async def test_get_historical_messages(graph_instance, mocker):
    thread_id = "test-thread-id"
    page = 1
    size = 2

    mock_messages = [
        HumanMessage(id="1", content="Hello"),
        AIMessage(id="2", content="Hi there!"),
        ToolMessage(
            id="3", content="Tool result", name="test_tool", tool_call_id="call_123"
        ),
    ]

    async def mock_get_messages(_thread_id):
        return mock_messages

    mocker.patch.object(
        graph_instance, "_get_latest_checkpoint_messages", side_effect=mock_get_messages
    )

    result = await graph_instance.get_historical_messages(thread_id, page, size)

    assert result is not None
    assert result.pagination.thread_id == thread_id
    assert result.pagination.current_page == page
    assert result.pagination.page_size == size
    assert result.pagination.total_messages == 3
    assert result.pagination.total_pages == 2

    assert len(result.messages) == 2
    assert result.messages[0].role == "user"
    assert result.messages[0].content == "Hello"

    assert result.messages[1].role == "assistant"
    assert result.messages[1].content == "Hi there!"

    graph_instance._get_latest_checkpoint_messages.assert_called_once_with(thread_id)


@pytest.mark.anyio
async def test_get_historical_messages_no_messages(graph_instance, mocker):
    thread_id = "test-thread-id"
    page = 1
    size = 5

    async def mock_get_no_messages(_thread_id):
        return None

    mocker.patch.object(
        graph_instance,
        "_get_latest_checkpoint_messages",
        side_effect=mock_get_no_messages,
    )

    result = await graph_instance.get_historical_messages(thread_id, page, size)

    assert result is None
    graph_instance._get_latest_checkpoint_messages.assert_called_once_with(thread_id)
