from langgraph.graph.state import StateGraph

from app.agentic.graph.graph import create_graph
from app.agentic.graph.state import ConversationState


def test_create_graph(user_id, mock_user_integrations_instance):
    created_graph = create_graph(user_id, mock_user_integrations_instance)

    assert isinstance(created_graph, StateGraph)
    assert "call_model" in created_graph.nodes
    assert "fetch_account" in created_graph.nodes
    assert "user_validation" in created_graph.nodes
    assert created_graph.output == ConversationState
    assert created_graph.compiled is False
