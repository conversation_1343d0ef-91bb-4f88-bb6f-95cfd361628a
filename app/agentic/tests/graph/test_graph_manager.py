import pytest
from langgraph.checkpoint.postgres.aio import Async<PERSON>ostgresSaver
from langgraph.graph.state import StateGraph

from app.agentic.graph.graph_instance import GraphInstance
from app.agentic.graph.graph_manager import Graph<PERSON>anager


@pytest.fixture
def mock_checkpointer(mocker):
    return mocker.Mock(spec=AsyncPostgresSaver)


@pytest.fixture
def mock_graph_definition(mocker):
    return mocker.Mock(spec=StateGraph)


@pytest.fixture
def mock_graph_instance(mocker):
    return mocker.Mock(spec=GraphInstance)


def test_graph_manager_initialization(mock_checkpointer):
    manager = GraphManager(checkpointer=mock_checkpointer)

    assert manager.checkpointer == mock_checkpointer


def test_get_graph_instance_creates_new_runner(
    mocker,
    user_id,
    mock_checkpointer,
    mock_graph_definition,
    mock_user_integrations_instance,
):
    mock_create_graph = mocker.patch(
        "app.agentic.graph.graph_manager.create_graph",
        return_value=mock_graph_definition,
    )
    mock_graph_instance_instance = mocker.Mock(spec=GraphInstance)
    mock_graph_instance_class = mocker.patch(
        "app.agentic.graph.graph_manager.GraphInstance",
        return_value=mock_graph_instance_instance,
    )

    manager = GraphManager(checkpointer=mock_checkpointer)

    runner = manager.get_graph_instance(user_id, mock_user_integrations_instance)

    assert runner == mock_graph_instance_instance
    mock_create_graph.assert_called_once_with(user_id, mock_user_integrations_instance)
    mock_graph_instance_class.assert_called_once_with(
        checkpointer=mock_checkpointer, graph_definition=mock_graph_definition
    )
