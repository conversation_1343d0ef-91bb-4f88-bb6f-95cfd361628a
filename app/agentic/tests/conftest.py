import datetime
import uuid

import pytest
from pytest_mock import Mo<PERSON><PERSON><PERSON><PERSON>

from app.auth.dependencies import get_authenticated_user_id
from app.integrations.protocols import CRMResource
from app.main import app
from app.workspace.dependencies import get_organization_service
from app.workspace.integrations.user_integrations import UserIntegrations
from app.workspace.schemas import OrganizationRead
from app.workspace.services.organization import OrganizationService


@pytest.fixture
def org_id():
    return uuid.uuid4()


@pytest.fixture
def user_id():
    return uuid.uuid4()


@pytest.fixture
def org_member_id():
    return uuid.uuid4()


@pytest.fixture
def mock_crm_provider(mocker):
    mock_provider = mocker.Mock(spec=CRMResource)
    mock_provider.get_opportunity = mocker.Mock(
        return_value={"Id": "001", "Name": "Test Opportunity"}
    )
    mock_provider.update_opportunity = mocker.Mock(
        return_value={"Id": "001", "Name": "Updated Opportunity"}
    )
    mock_provider.list_opportunities_by_account = mocker.Mock(
        return_value=[{"Id": "001", "Name": "Test Opportunity"}]
    )
    mock_provider.get_account = mocker.Mock(
        return_value={"Id": "002", "Name": "Test Account"}
    )
    return mock_provider


@pytest.fixture
def mock_user_integrations_instance(mocker, mock_crm_provider):
    instance = mocker.Mock(spec=UserIntegrations)
    instance.crm = mocker.Mock(return_value=mock_crm_provider)
    return instance


@pytest.fixture(autouse=True)
def mock_user_integrations_class(mocker, mock_user_integrations_instance):
    mock = mocker.patch("app.agentic.context.tools.UserIntegrations")
    mock.return_value = mock_user_integrations_instance
    return mock


@pytest.fixture(autouse=True)
def override_authenticated_user_id():
    def mock_get_authenticated_user_id():
        return "mocked_user_id"

    app.dependency_overrides[get_authenticated_user_id] = mock_get_authenticated_user_id
    yield
    app.dependency_overrides.pop(get_authenticated_user_id)


@pytest.fixture(autouse=True)
def override_organization_service(mocker: MockerFixture):
    mock_service = mocker.MagicMock(spec=OrganizationService)
    now = datetime.datetime.now(datetime.UTC)
    org = OrganizationRead(
        id=uuid.uuid4(),
        name="Test Org",
        domain="test.org",
        is_active=True,
        created_at=now,
        updated_at=now,
    )

    mock_service.get_user_organization.return_value = org

    app.dependency_overrides[get_organization_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_organization_service)
