from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi import <PERSON><PERSON><PERSON>
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.graph.state import CompiledStateGraph, StateGraph
from psycopg_pool import Async<PERSON>onnectionPool
from pytest_mock import Mo<PERSON><PERSON>ix<PERSON>

from app.agentic.graph.graph_manager import GraphManager
from app.agentic.lifespan import lifespan


@pytest.fixture
def mock_app() -> FastAPI:
    app = FastAPI()
    app.state.checkpoint_db_pool = None
    app.state.graph_manager = None
    return app


@pytest.fixture
def mock_pool_instance(mocker: MockerFixture) -> AsyncMock:
    instance = mocker.AsyncMock(spec=AsyncConnectionPool)
    instance.open = AsyncMock()
    instance.close = AsyncMock()
    return instance


@pytest.fixture
def mock_pool_class(mocker: MockerFixture, mock_pool_instance: AsyncMock) -> MagicMock:
    return mocker.patch(
        "app.agentic.lifespan.AsyncConnectionPool",
        return_value=mock_pool_instance,
    )


@pytest.fixture
def mock_checkpointer_instance(mocker: Mo<PERSON>Fixture) -> AsyncMock:
    instance = mocker.AsyncMock(spec=AsyncPostgresSaver)
    instance.setup = AsyncMock()
    return instance


@pytest.fixture
def mock_checkpointer_class(
    mocker: MockerFixture, mock_checkpointer_instance: AsyncMock
) -> MagicMock:
    return mocker.patch(
        "app.agentic.lifespan.AsyncPostgresSaver",
        return_value=mock_checkpointer_instance,
    )


@pytest.fixture
def mock_graph_obj(mocker: MockerFixture) -> MagicMock:
    graph_mock = mocker.Mock(spec=StateGraph)
    graph_mock.compile.return_value = MagicMock(spec=CompiledStateGraph)
    return graph_mock


@pytest.fixture
def mock_graph_manager_instance(mocker: MockerFixture) -> MagicMock:
    return mocker.Mock(spec=GraphManager)


@pytest.fixture
def mock_graph_manager_class(
    mocker: MockerFixture, mock_graph_manager_instance: MagicMock
) -> MagicMock:
    return mocker.patch(
        "app.agentic.lifespan.GraphManager",
        return_value=mock_graph_manager_instance,
    )


@pytest.mark.anyio
async def test_lifespan_setup_and_teardown(
    mock_app: FastAPI,
    mock_pool_class: MagicMock,
    mock_pool_instance: AsyncMock,
    mock_checkpointer_class: MagicMock,
    mock_checkpointer_instance: AsyncMock,
    mock_graph_manager_class: MagicMock,
    mock_graph_manager_instance: MagicMock,
):
    async with lifespan(mock_app):
        assert mock_pool_class is not None
        mock_pool_instance.open.assert_called_once()

        mock_checkpointer_class.assert_called_once_with(mock_pool_instance)
        mock_checkpointer_instance.setup.assert_called_once()

        mock_graph_manager_class.assert_called_once_with(
            checkpointer=mock_checkpointer_instance,
        )

        assert mock_app.state.checkpoint_db_pool is mock_pool_instance
        assert mock_app.state.graph_manager is mock_graph_manager_instance

    mock_pool_instance.close.assert_called_once()
    assert mock_app.state.checkpoint_db_pool is None
    assert mock_app.state.graph_manager is None
