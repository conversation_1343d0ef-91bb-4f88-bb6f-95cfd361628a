import asyncio

import pytest

from app.agentic.context.schemas import (
    GetAccount,
    GetOpportunity,
    ListOpportunitiesByAccount,
    ToolDefinition,
    UpdateOpportunity,
)
from app.agentic.context.tools import _ensure_async, get_tools


def test_get_tools(
    mocker,
    user_id,
    mock_user_integrations_instance,
    mock_crm_provider,
):
    mock_crm_provider.get_opportunity = mocker.MagicMock()
    mock_crm_provider.update_opportunity = mocker.MagicMock()
    mock_crm_provider.list_opportunities_by_account = mocker.MagicMock()
    mock_crm_provider.get_account = mocker.MagicMock()

    tools = get_tools(user_id, mock_user_integrations_instance)

    mock_user_integrations_instance.crm.assert_called_once()

    assert len(tools) == 4

    tool_details = {
        "get_opportunity": (
            "Fetch a CRM opportunity by its ID",
            GetOpportunity,
            mock_crm_provider.get_opportunity,
        ),
        "update_opportunity": (
            "Update a CRM opportunity with provided fields",
            UpdateOpportunity,
            mock_crm_provider.update_opportunity,
        ),
        "list_opportunities_by_account": (
            "List CRM opportunities for a given account",
            ListOpportunitiesByAccount,
            mock_crm_provider.list_opportunities_by_account,
        ),
        "get_account": (
            "Fetch a CRM account by its ID",
            GetAccount,
            mock_crm_provider.get_account,
        ),
    }

    expected_names = list(tool_details.keys())
    actual_names = [tool.name for tool in tools]
    assert set(actual_names) == set(expected_names)

    for tool in tools:
        assert isinstance(tool, ToolDefinition)
        assert tool.name in tool_details
        expected_description, expected_schema, _ = tool_details[tool.name]
        assert tool.description == expected_description
        assert tool.args_schema == expected_schema
        assert tool.coroutine is not None
        assert asyncio.iscoroutinefunction(tool.coroutine)


@pytest.mark.anyio
async def test_tool_functions(
    user_id,
    mock_crm_provider,
    mock_user_integrations_instance,
):
    tools = get_tools(user_id, mock_user_integrations_instance)

    tool_map = {t.name: t for t in tools}

    get_opportunity_tool = tool_map["get_opportunity"]
    update_opportunity_tool = tool_map["update_opportunity"]
    list_opportunities_by_account_tool = tool_map["list_opportunities_by_account"]
    get_account_tool = tool_map["get_account"]

    mock_crm_provider.get_opportunity.reset_mock()
    mock_crm_provider.update_opportunity.reset_mock()
    mock_crm_provider.list_opportunities_by_account.reset_mock()
    mock_crm_provider.get_account.reset_mock()

    await get_opportunity_tool.coroutine("001")
    mock_crm_provider.get_opportunity.assert_called_once_with("001")

    fields = {"Name": "Updated Opportunity"}
    await update_opportunity_tool.coroutine("001", fields)
    mock_crm_provider.update_opportunity.assert_called_once_with("001", fields)

    await list_opportunities_by_account_tool.coroutine("account123")
    mock_crm_provider.list_opportunities_by_account.assert_called_once_with(
        "account123"
    )

    await get_account_tool.coroutine("002")
    mock_crm_provider.get_account.assert_called_once_with("002")


def test_get_tools_no_integration(
    user_id,
    mock_user_integrations_instance,
) -> None:
    mock_user_integrations_instance.crm.return_value = None

    with pytest.raises(
        RuntimeError,
        match=f"No CRM integration configured for user {user_id}",
    ):
        get_tools(user_id, mock_user_integrations_instance)

    mock_user_integrations_instance.crm.assert_called_once()


@pytest.mark.anyio
async def test_ensure_async_sync_function() -> None:
    def sync_func(x: int) -> int:
        return x * 2

    async_func = _ensure_async(sync_func)
    assert asyncio.iscoroutinefunction(async_func)
    result = await async_func(5)
    assert result == 10


@pytest.mark.anyio
async def test_ensure_async_already_async() -> None:
    async def async_func(x: int) -> int:
        return x * 2

    wrapped_func = _ensure_async(async_func)
    assert wrapped_func is async_func
    result = await wrapped_func(5)
    assert result == 10
