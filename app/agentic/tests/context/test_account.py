import pytest

from app.agentic.context.account import get_account_details


def test_get_account_details(
    user_id,
    mock_user_integrations_instance,
    mock_crm_provider,
):
    crm_account_id = "account123"

    account_details = get_account_details(
        user_id,
        crm_account_id,
        mock_user_integrations_instance,
    )

    mock_user_integrations_instance.crm.assert_called_once()
    mock_crm_provider.get_account.assert_called_once_with(crm_account_id)
    assert account_details == {"Id": "002", "Name": "Test Account"}


def test_get_account_details_no_integration(
    user_id,
    mock_user_integrations_instance,
):
    mock_user_integrations_instance.crm.return_value = None
    crm_account_id = "account123"

    with pytest.raises(
        RuntimeError,
        match=f"No CRM integration configured for user {user_id}",
    ):
        get_account_details(user_id, crm_account_id, mock_user_integrations_instance)

    mock_user_integrations_instance.crm.assert_called_once()
