import typing
from datetime import UTC, datetime
from typing import Any, Literal

from langchain_core.messages import AIMessage, BaseMessage, SystemMessage
from langchain_core.tools import StructuredTool
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.types import Command, interrupt
from pydantic import SecretStr

from app.agentic.context.account import get_account_details
from app.agentic.graph.prompts import AgentPrompts
from app.agentic.graph.state import ConversationState
from app.core.config import config
from app.workspace.integrations.user_integrations import UserIntegrations


def get_llm() -> ChatGoogleGenerativeAI:
    return ChatGoogleGenerativeAI(
        model="gemini-2.5-flash-preview-04-17",
        api_key=SecretStr(config.gemini_api_key),
    )


def call_model(state: ConversationState, tools: list[StructuredTool]) -> dict:
    messages = state["messages"]
    account_info = state.get("account_info")

    llm_messages: list[BaseMessage] = [
        SystemMessage(content=AgentPrompts.get_sales_assistant_system_prompt())
    ]

    if account_info:
        account_context_content = AgentPrompts.format_account_context_message(
            account_info
        )
        llm_messages.append(SystemMessage(content=account_context_content))

    llm_messages.extend(messages)
    llm_with_tools = get_llm().bind_tools(tools)
    response = llm_with_tools.invoke(llm_messages)

    ai_response = typing.cast("AIMessage", response)

    return {"messages": [ai_response]}


def fetch_account(
    state: ConversationState,
    user_integrations: UserIntegrations,
) -> ConversationState:
    info = get_account_details(
        state["user_id"],
        state["crm_account_id"],
        user_integrations,
    )

    state["account_info"] = info
    state["last_refetch_at"] = datetime.now(UTC)

    return state


def user_validation(
    state: ConversationState,
) -> Command[Literal["call_model", "call_tools"]]:
    last_message = state["messages"][-1]
    update_details: dict[str, Any] = {}

    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        for tool_call in last_message.tool_calls:
            if tool_call["name"] == "update_opportunity":
                update_details = tool_call["args"]

    decision = interrupt(
        {
            "question": "Do these updates in your CRM look correct?",
            "update_to_be_made": update_details,
        }
    )

    if decision == "approve":
        return Command(goto="call_tools", update={"decision": "approved"})
    else:
        rejection_message = AIMessage(
            content="The user has rejected the proposed CRM update. Please revise your approach or ask for more information."
        )
        return Command(
            goto="call_model",
            update={
                "decision": "rejected",
                "messages": state["messages"] + [rejection_message],
            },
        )
