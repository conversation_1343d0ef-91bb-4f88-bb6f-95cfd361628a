import functools
from uuid import UUID

from langchain_core.tools import StructuredTool
from langgraph.graph import E<PERSON>, START, StateGraph
from langgraph.prebuilt import ToolNode

from app.agentic.context.tools import get_tools
from app.agentic.graph.nodes import call_model, fetch_account, user_validation
from app.agentic.graph.state import ConversationState
from app.workspace.integrations.user_integrations import UserIntegrations


def create_graph(
    user_id: UUID,
    user_integrations: UserIntegrations,
) -> StateGraph:
    raw_tools = get_tools(user_id, user_integrations)
    langchain_tools = [
        StructuredTool(
            name=td.name,
            description=td.description,
            func=None,
            coroutine=td.coroutine,
            args_schema=td.args_schema,
        )
        for td in raw_tools
    ]

    tool_node_instance = ToolNode(langchain_tools)
    partial_call_model = functools.partial(call_model, tools=langchain_tools)
    partial_fetch_account = functools.partial(
        fetch_account, user_integrations=user_integrations
    )

    graph = StateGraph(state_schema=ConversationState)

    graph.add_node("fetch_account", partial_fetch_account)
    graph.add_node("call_model", partial_call_model)
    graph.add_node("user_validation", user_validation)
    graph.add_node("call_tools", tool_node_instance)

    graph.add_conditional_edges(
        START, should_fetch_account, ["fetch_account", "call_model"]
    )
    graph.add_edge("fetch_account", "call_model")
    graph.add_conditional_edges(
        "call_model",
        should_execute_action,
        ["call_tools", "user_validation", "fetch_account", END],
    )
    graph.add_edge("call_tools", "call_model")

    return graph


def should_fetch_account(state: ConversationState) -> str:
    return "fetch_account" if not state.get("last_refetch_at") else "call_model"


def should_execute_action(state: ConversationState) -> str:
    last = state["messages"][-1]
    if hasattr(last, "tool_calls") and last.tool_calls:
        for tool_call in last.tool_calls:
            if tool_call["name"] == "update_opportunity":
                return "user_validation"

        return "call_tools"
    if isinstance(last.content, str) and last.content.strip().lower().startswith(
        "/refresh"
    ):
        return "fetch_account"

    return END
