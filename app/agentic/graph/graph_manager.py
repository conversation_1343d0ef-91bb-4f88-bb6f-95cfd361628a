from uuid import UUID

from langgraph.checkpoint.postgres.aio import Async<PERSON>ostgresSaver

from app.agentic.graph.graph import create_graph
from app.agentic.graph.graph_instance import GraphInstance
from app.workspace.integrations.user_integrations import UserIntegrations


class GraphManager:
    def __init__(self, checkpointer: AsyncPostgresSaver):
        self.checkpointer = checkpointer

    def get_graph_instance(
        self,
        user_id: UUID,
        user_integrations: UserIntegrations,
    ) -> GraphInstance:
        graph_definition = create_graph(user_id, user_integrations)
        return GraphInstance(
            checkpointer=self.checkpointer, graph_definition=graph_definition
        )
