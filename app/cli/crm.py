import uuid

import typer

from app.workspace.integrations.workflows import CRMWorkflow
from app.workspace.types import EnvironmentType

app = typer.Typer(help="Salesforce data management commands", no_args_is_help=True)


@app.command()
def sync_account_access(
    org_id: uuid.UUID = typer.Option(..., help="Organization ID"),
    env: EnvironmentType = typer.Option(EnvironmentType.PROD, help="Environment type"),
    interval: int = typer.Option(300, help="Override execution interval in seconds"),
    daemon: bool = typer.Option(
        False, "--daemon", "-d", help="Run in daemon mode (continuous execution)"
    ),
):
    try:
        with CRMWorkflow(org_id=org_id, env_type=env) as crm_workflow:
            result = crm_workflow.sync_account_access(
                interval=interval,
                daemon=daemon,
            )

        if result["status"] == "success":
            if daemon:
                typer.echo("Salesforce account access sync daemon has stopped")
            else:
                typer.echo("Salesforce account access sync completed successfully!")
                typer.echo(
                    f"Processed {result['user_count']} users from organization {org_id}"
                )
                if result.get("details"):
                    typer.echo(str(result["details"]))

                if result.get("failed_users"):
                    typer.echo(
                        f"\nWarning: Could not sync {len(result['failed_users'])} users due to missing CRM credentials"
                    )
        else:
            typer.echo(f"Error: {result.get('error', 'Unknown error')}")
            raise typer.Exit(code=1)

    except Exception as e:
        typer.echo(f"Error during Salesforce account access sync: {e}")
        raise typer.Exit(code=1)
