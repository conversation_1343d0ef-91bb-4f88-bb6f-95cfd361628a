import uuid

import typer

from app.core.database import SessionLocal
from app.integrations.processors.embedders.noop_embedder import <PERSON><PERSON><PERSON><PERSON>dder
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import IntegrationSource

app = typer.Typer(help="Document search commands")


@app.command()
def similarity_search(
    tenant_id: uuid.UUID = typer.Option(..., help="Tenant ID"),
    source: IntegrationSource = typer.Option(..., help="Source ID"),
    query: str = typer.Option(..., help="Query text"),
    limit: int = typer.Option(5, help="Maximum number of results"),
):
    embedder = NoopEmbedder()
    query_embedding = embedder.embed_text(query)

    with SessionLocal() as session:
        store = PostgresDocumentStore(session, tenant_id, source)
        results = store.find_similar_documents(query_embedding, limit)

    typer.echo(f"Found {len(results)} similar documents:")
    for doc, score in results:
        typer.echo(f"ID: {doc.id}")
        typer.echo(f"Content: {doc.content}")
        typer.echo(f"Timestamp: {doc.source_timestamp}")
        typer.echo(f"Tags: {doc.tags}")
        typer.echo(f"Score: {score}")
        typer.echo("---")
