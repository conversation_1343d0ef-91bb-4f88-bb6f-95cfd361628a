from datetime import UTC, datetime, timedelta

import pytest

from app.auth.exceptions import (
    AuthTokenDecodeError,
    OneTimeCredentialsCodeError,
    OneTimeCredentialsTokenError,
)
from app.auth.models import User
from app.auth.repository import (
    OneTimeCredentialsRepository,
    RefreshTokenRepository,
    UserRepository,
)
from app.auth.schemas import UserTokens
from app.auth.security_utilities import (
    TokenType,
    create_jwt_token,
    decode_token,
)
from app.auth.service import AuthService
from app.common.email.email_service import EmailService
from app.common.email.local_email_client import LocalEmailClient
from app.common.email.template_renderer import TemplateRenderer


@pytest.fixture
def auth_service(db_session) -> AuthService:
    return AuthService(
        db_session=db_session,
        user_repository=UserRepository(db_session),
        refresh_token_repository=RefreshTokenRepository(db_session),
        one_time_credentials_repository=OneTimeCredentialsRepository(db_session),
        email_service=EmailService(
            template_renderer=TemplateRenderer(template_dir="app/auth/email_templates"),
            email_client=LocalEmailClient(),
        ),
    )


@pytest.fixture
def user_and_tokens(auth_service: AuthService):
    user = auth_service.user_repository.create(
        email="<EMAIL>",
        first_name="John",
        last_name="Doe",
    )
    tokens = auth_service.create_new_tokens(user.id)
    return user, tokens


def test_create_user(auth_service: AuthService):
    user = auth_service.create_user(
        email="<EMAIL>", first_name="John", last_name="Doe"
    )
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.first_name == "John"
    assert user.last_name == "Doe"


def test_add_refresh_token(
    auth_service: AuthService, user_and_tokens: tuple[User, UserTokens]
):
    user, tokens = user_and_tokens

    first_refresh_token = auth_service.refresh_token_repository.get_by_token(
        tokens.refresh_token
    )
    assert first_refresh_token is not None
    first_refresh_token_id = first_refresh_token.id

    for _ in range(4):
        auth_service.refresh_token_repository.create(
            user_id=user.id,
            token="refresh_token",  # noqa: S106
            expires_at=datetime.now(UTC),
        )

    auth_service._add_refresh_token(user.id, "refresh_token", datetime.now(UTC))

    refresh_tokens = auth_service.refresh_token_repository.get_non_revoked_by_user_id(
        user.id
    )
    assert len(refresh_tokens) == 5
    assert first_refresh_token_id not in [rt.id for rt in refresh_tokens]


def test_revoke_refresh_token_and_create_new_tokens(
    auth_service: AuthService, user_and_tokens: tuple[User, UserTokens]
):
    user, tokens = user_and_tokens
    valid_refresh_token = tokens.refresh_token

    fake_jwt = "fake_jwt"
    with pytest.raises(AuthTokenDecodeError) as excinfo:
        auth_service.revoke_refresh_token_and_create_new_tokens(fake_jwt)
    assert "Token is invalid" in str(excinfo.value)

    expired_jwt = create_jwt_token(
        {
            "sub": str(user.id),
            "exp": datetime.now(UTC) - timedelta(minutes=1),
            "type": TokenType.REFRESH.value,
        }
    )
    with pytest.raises(AuthTokenDecodeError) as excinfo:
        auth_service.revoke_refresh_token_and_create_new_tokens(expired_jwt)
    assert "Token has expired." in str(excinfo.value)

    wrong_type_jwt = create_jwt_token(
        {
            "sub": str(user.id),
            "exp": datetime.now(UTC) + timedelta(minutes=1),
            "type": TokenType.ACCESS.value,
        }
    )
    with pytest.raises(AuthTokenDecodeError) as excinfo:
        auth_service.revoke_refresh_token_and_create_new_tokens(wrong_type_jwt)
    assert "Inconsistent token type." in str(excinfo.value)

    auth_service.revoke_refresh_token_and_create_new_tokens(valid_refresh_token)

    token_from_db = auth_service.refresh_token_repository.get_by_token(
        valid_refresh_token
    )
    assert token_from_db is not None
    assert token_from_db.revoked_at is not None


def test_logout(auth_service: AuthService, user_and_tokens: tuple[User, UserTokens]):
    _, tokens = user_and_tokens
    refresh_token = tokens.refresh_token

    auth_service.logout(tokens.access_token, refresh_token)

    token_from_db = auth_service.refresh_token_repository.get_by_token(refresh_token)
    assert token_from_db is not None
    assert token_from_db.revoked_at is not None


def test_maybe_create_and_send_via_email_one_time_credentials__unknown_email(
    auth_service: AuthService,
):
    email = "<EMAIL>"

    otc_token = auth_service.maybe_create_and_send_via_email_one_time_credentials(email)

    assert otc_token is not None

    decrypted_access_token = decode_token(otc_token.token, TokenType.LOGIN)
    assert decrypted_access_token["sub"] == email

    one_time_credentials = auth_service.one_time_credentials_repository.get_by_token(
        otc_token.token
    )
    # No record in the database
    assert one_time_credentials is None

    # No email sent
    assert len(auth_service.email_service.email_client.outbox) == 0


def test_maybe_create_and_send_via_email_one_time_credentials__known_email(
    auth_service: AuthService,
    user_and_tokens: tuple[User, UserTokens],
):
    user, _ = user_and_tokens
    email = user.email

    otc_token = auth_service.maybe_create_and_send_via_email_one_time_credentials(email)

    assert otc_token is not None

    decrypted_access_token = decode_token(otc_token.token, TokenType.LOGIN)
    assert decrypted_access_token["sub"] == email

    one_time_credentials = auth_service.one_time_credentials_repository.get_by_token(
        otc_token.token
    )
    assert one_time_credentials is not None
    assert one_time_credentials.user_id == user.id
    assert one_time_credentials.used_at is None

    assert len(auth_service.email_service.email_client.outbox) == 1
    sent_email = auth_service.email_service.email_client.outbox[0]
    assert one_time_credentials.code in sent_email["subject"]
    assert one_time_credentials.code in sent_email["body"]
    assert one_time_credentials.token in sent_email["body"]


def test_verify_one_time_credentials_token__success(
    auth_service: AuthService,
    user_and_tokens: tuple[User, UserTokens],
):
    user, _ = user_and_tokens
    email = user.email

    otc_token = auth_service.maybe_create_and_send_via_email_one_time_credentials(email)

    one_time_credentials = auth_service.verify_one_time_credentials_token(
        otc_token.token
    )
    assert one_time_credentials is not None


def test_verify_one_time_credentials_token__not_found(
    auth_service: AuthService,
):
    token = create_jwt_token(
        {
            "sub": "<EMAIL>",
            "type": TokenType.LOGIN.value,
            "exp": datetime.now(UTC) + timedelta(minutes=1),
        }
    )

    with pytest.raises(OneTimeCredentialsTokenError) as excinfo:
        auth_service.verify_one_time_credentials_token(token)
    assert "One time credentials not found" in str(excinfo.value)


def test_verify_one_time_credentials_token__already_used(
    auth_service: AuthService,
    user_and_tokens: tuple[User, UserTokens],
):
    user, _ = user_and_tokens
    email = user.email

    otc_token = auth_service.maybe_create_and_send_via_email_one_time_credentials(email)
    otc = auth_service.one_time_credentials_repository.get_by_token(otc_token.token)
    assert otc is not None
    otc.used_at = datetime.now(UTC)
    auth_service.db_session.commit()

    with pytest.raises(OneTimeCredentialsTokenError) as excinfo:
        auth_service.verify_one_time_credentials_token(otc_token.token)
    assert "One time credentials already used" in str(excinfo.value)


def test_verify_one_time_credentials_code__success(
    auth_service: AuthService,
    user_and_tokens: tuple[User, UserTokens],
):
    user, _ = user_and_tokens
    email = user.email

    otc_token = auth_service.maybe_create_and_send_via_email_one_time_credentials(email)
    otc = auth_service.one_time_credentials_repository.get_by_token(otc_token.token)
    assert otc is not None
    code = otc.code

    user_id = auth_service.verify_one_time_credentials_code(otc_token.token, code)
    assert user_id == user.id
    otc = auth_service.one_time_credentials_repository.get_by_token(otc_token.token)
    assert otc is not None
    assert otc.used_at is not None


def test_verify_one_time_credentials_code__wrong_code(
    auth_service: AuthService,
    user_and_tokens: tuple[User, UserTokens],
):
    user, _ = user_and_tokens
    email = user.email

    otc_token = auth_service.maybe_create_and_send_via_email_one_time_credentials(email)

    with pytest.raises(OneTimeCredentialsCodeError) as excinfo:
        auth_service.verify_one_time_credentials_code(otc_token.token, "wrong_code")
    assert "Invalid one time credentials code" in str(excinfo.value)
