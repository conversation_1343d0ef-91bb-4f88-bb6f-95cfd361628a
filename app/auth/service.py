from datetime import UTC, datetime, timedelta
from uuid import UUID

from sqlalchemy.orm import Session

from app.auth.exceptions import (
    AuthTokenRevocationError,
    OneTimeCredentialsCodeError,
    OneTimeCredentialsTokenError,
)
from app.auth.models import OneTimeCredentials, RefreshToken, User
from app.auth.repository import (
    OneTimeCredentialsRepository,
    RefreshTokenRepository,
    UserRepository,
)
from app.auth.schemas import UserOtcToken, UserTokens
from app.auth.security_utilities import (
    ACCESS_TOKEN_EXPIRE_MINUTES,
    MAX_REFRESH_TOKENS,
    ONE_TIME_CREDENTIALS_EXPIRE_MINUTES,
    REFRESH_TOKEN_EXPIRE_DAYS,
    TokenType,
    create_jwt_token,
    decode_token,
    generate_auth_code,
)
from app.common.email.email_service import EmailService
from app.common.helpers.logger import get_logger
from app.core.config import config

logger = get_logger()


class AuthService:
    def __init__(
        self,
        db_session: Session,
        user_repository: UserRepository,
        refresh_token_repository: RefreshTokenRepository,
        one_time_credentials_repository: OneTimeCredentialsRepository,
        email_service: EmailService,
    ):
        self.db_session = db_session
        self.user_repository = user_repository
        self.refresh_token_repository = refresh_token_repository
        self.one_time_credentials_repository = one_time_credentials_repository
        self.email_service = email_service

    def create_user(self, email: str, first_name: str, last_name: str) -> User:
        user = self.user_repository.create(
            email=email, first_name=first_name, last_name=last_name
        )
        self.db_session.commit()
        return user

    def get_user_by_email(self, email: str) -> User | None:
        return self.user_repository.get_by_email(email)

    def _get_user_by_id_or_raise(self, user_id: UUID) -> User:
        user = self.user_repository.get_by_id(user_id)
        if user is None:
            raise ValueError("User not found")
        return user

    def create_new_tokens(self, user_id: UUID, commit: bool = True) -> UserTokens:
        base_token_data = {"sub": str(user_id)}
        now = datetime.now(UTC)
        access_token = create_jwt_token(
            {
                **base_token_data,
                "type": TokenType.ACCESS.value,
                "exp": now + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES),
            }
        )
        refresh_token_expires_at = now + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        refresh_token = create_jwt_token(
            {
                **base_token_data,
                "type": TokenType.REFRESH.value,
                "exp": refresh_token_expires_at,
            }
        )
        self._add_refresh_token(user_id, refresh_token, refresh_token_expires_at)
        if commit:
            self.db_session.commit()
        return UserTokens(access_token=access_token, refresh_token=refresh_token)

    def _add_refresh_token(
        self, user_id: UUID, refresh_token: str, expires_at: datetime
    ) -> RefreshToken:
        has_max_tokens = (
            len(
                refresh_tokens
                := self.refresh_token_repository.get_non_revoked_by_user_id(user_id)
            )
            >= MAX_REFRESH_TOKENS
        )
        if has_max_tokens:
            oldest_refresh_token = sorted(refresh_tokens, key=lambda rt: rt.created_at)[
                0
            ]
            self._revoke_refresh_token(refresh_token_id=oldest_refresh_token.id)
        return self.refresh_token_repository.create(
            user_id=user_id,
            token=refresh_token,
            expires_at=expires_at,
        )

    def revoke_refresh_token_and_create_new_tokens(
        self, refresh_token: str
    ) -> UserTokens:
        token_content = decode_token(refresh_token, token_type=TokenType.REFRESH)
        self._revoke_refresh_token(refresh_token=refresh_token)
        user_id = UUID(token_content.get("sub"))
        self._get_user_by_id_or_raise(user_id)
        tokens = self.create_new_tokens(user_id, commit=False)
        self.db_session.commit()
        return tokens

    def _revoke_refresh_token(
        self, *, refresh_token: str | None = None, refresh_token_id: UUID | None = None
    ) -> None:
        if refresh_token is None == refresh_token_id is None:
            raise ValueError(
                "Either refresh_token or refresh_token_id must be provided"
            )
        if refresh_token_id is not None:
            token = self.refresh_token_repository.get_by_id(refresh_token_id)
        elif refresh_token is not None:
            token = self.refresh_token_repository.get_by_token(refresh_token)

        if token is None:
            raise AuthTokenRevocationError("Refresh token not found")
        if token.revoked_at is not None:
            raise AuthTokenRevocationError("Refresh token already revoked")
        self.refresh_token_repository.update(token.id, revoked_at=datetime.now(UTC))

    def get_user_id_from_access_token(self, token: str) -> UUID:
        token_content = decode_token(token, token_type=TokenType.ACCESS)
        user_id = UUID(token_content.get("sub"))
        self._get_user_by_id_or_raise(user_id)
        return user_id

    def logout(
        self,
        access_token: str,
        refresh_token: str,
    ) -> None:
        try:
            self._revoke_refresh_token(refresh_token=refresh_token)
            self._blacklist_access_token(access_token)
        except Exception:
            # Don't block logout
            logger.exception("Error while logging out user")

    def _blacklist_access_token(self, access_token: str) -> None:
        # TODO: implement the logic to blacklist the access token with Redis
        pass

    def maybe_create_and_send_via_email_one_time_credentials(
        self,
        email: str,
    ) -> UserOtcToken:
        expires_at = datetime.now(UTC) + timedelta(
            minutes=ONE_TIME_CREDENTIALS_EXPIRE_MINUTES
        )
        token = create_jwt_token(
            {
                "sub": email,
                "type": TokenType.LOGIN.value,
                "exp": expires_at,
            }
        )
        # We don't want to leak information about the existence of the email in the system,
        # so we always return a token to the frontend, but it is persisted/sent by email
        # only if the user exists
        if user := self.user_repository.get_by_email(email):
            self._create_and_send_via_email_one_time_credentials(
                user.id, token, expires_at
            )
        # Only the token is returned to the client to allow login from the current window,
        # the code needs to be retrieved from the email
        return UserOtcToken(token=token)

    def _create_and_send_via_email_one_time_credentials(
        self,
        user_id: UUID,
        token: str,
        expires_at: datetime,
    ) -> None:
        otc = self.one_time_credentials_repository.create(
            user_id=user_id,
            token=token,
            code=generate_auth_code(),
            expires_at=expires_at,
        )
        self.db_session.commit()
        otc_link = f"{config.frontend_url}/login/email?token={otc.token}"
        logger.debug(f"📝 LOGIN URL: {otc_link}")
        logger.debug(f"📝 LOGIN CODE: {otc.code}")
        self._send_one_time_credentials_email(
            user_id=user_id,
            otc_link=otc_link,
            otc_code=otc.code,
        )

    def verify_one_time_credentials_token(self, token: str) -> OneTimeCredentials:
        # Ensure the token is valid, namely not expired
        decode_token(token, token_type=TokenType.LOGIN)
        one_time_credentials = self.one_time_credentials_repository.get_by_token(token)
        if one_time_credentials is None:
            raise OneTimeCredentialsTokenError("One time credentials not found")
        if one_time_credentials.used_at is not None:
            raise OneTimeCredentialsTokenError("One time credentials already used")
        return one_time_credentials

    def verify_one_time_credentials_code(self, token: str, code: str) -> UUID:
        one_time_credentials = self.verify_one_time_credentials_token(token)
        if one_time_credentials.code != code:
            raise OneTimeCredentialsCodeError("Invalid one time credentials code")
        self.one_time_credentials_repository.update(
            id=one_time_credentials.id,
            used_at=datetime.now(UTC),
        )
        self.db_session.commit()
        return one_time_credentials.user_id

    def _send_one_time_credentials_email(
        self,
        user_id: UUID,
        otc_link: str,
        otc_code: str,
    ) -> None:
        user = self._get_user_by_id_or_raise(user_id)
        self.email_service.send_email(
            to=user.email,
            template_name="one_time_credentials.html",
            name=user.full_name,
            otc_link=otc_link,
            otc_code=otc_code,
        )
