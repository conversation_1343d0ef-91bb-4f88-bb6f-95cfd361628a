from uuid import UUID

from sqlalchemy.orm import Session

from app.auth.models import OneTimeCredentials, RefreshToken, User
from app.common.orm.base_repository import BaseRepository


class UserRepository(BaseRepository[User]):
    def __init__(self, db_session: Session):
        super().__init__(db_session, User)

    def get_by_email(self, email: str) -> User | None:
        res = self._get_by_attrs(email=email)
        return res[0] if res else None


class RefreshTokenRepository(BaseRepository[RefreshToken]):
    def __init__(self, db_session: Session):
        super().__init__(db_session, RefreshToken)

    def get_non_revoked_by_user_id(self, user_id: UUID) -> list[RefreshToken]:
        return self._get_by_attrs(
            user_id=user_id,
            revoked_at=None,
        )

    def get_by_token(self, token: str) -> RefreshToken | None:
        res = self._get_by_attrs(token=token)
        return res[0] if res else None


class OneTimeCredentialsRepository(BaseRepository[OneTimeCredentials]):
    def __init__(self, db_session: Session):
        super().__init__(db_session, OneTimeCredentials)

    def get_by_token(self, token: str) -> OneTimeCredentials | None:
        res = self._get_by_attrs(token=token)
        return res[0] if res else None
