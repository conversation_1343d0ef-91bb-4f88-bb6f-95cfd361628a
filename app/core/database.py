import datetime
import uuid
from typing import Annotated

from fastapi import Depends
from sqlalchemy import UUID, DateTime, create_engine
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    Session,
    mapped_column,
    sessionmaker,
)

from app.core.config import config

engine = create_engine(str(config.database.database_url), echo=config.debug)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


class BaseModel(DeclarativeBase):
    __abstract__ = True

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
    )

    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.datetime.now(datetime.UTC),
    )

    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.datetime.now(datetime.UTC),
        onupdate=lambda: datetime.datetime.now(datetime.UTC),
    )

    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id}>"


def get_db_session():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


DbSessionDep = Annotated[Session, Depends(get_db_session)]
