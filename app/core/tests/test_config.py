import importlib
import types

import pytest

from app.core.config import AppConfig, DatabaseConfig
from app.core.env import Environment


def reload_config_module(_monkeypatch) -> tuple[AppConfig, types.ModuleType]:
    """
    Reloads the app.core.config module to force re-reading the APP_ENV environment variable.
    Returns a tuple of the new config instance and the reloaded module.
    """
    import app.core.config as config_mod

    importlib.reload(config_mod)
    return config_mod.config, config_mod


def test_production_config(monkeypatch):
    # Set APP_ENV to production and reload the module.
    monkeypatch.setenv("APP_ENV", Environment.PRODUCTION)
    config, config_mod = reload_config_module(monkeypatch)
    # Use the ProdConfig from the reloaded module for comparison.
    assert isinstance(config, config_mod.ProdConfig), (
        "Expected a ProdConfig instance in production environment"
    )


def test_test_config(monkeypatch):
    # Set APP_ENV to test and reload the module.
    monkeypatch.setenv("APP_ENV", Environment.TEST)
    config, config_mod = reload_config_module(monkeypatch)
    assert isinstance(config, config_mod.TestConfig), (
        "Expected a TestConfig instance in test environment"
    )
    # In TestConfig, log_level is explicitly set.
    assert config.log_level == "WARNING", (
        "Expected log_level to be 'WARNING' for TestConfig"
    )


def test_development_config(monkeypatch):
    # Set APP_ENV to a value other than production or test (e.g., development).
    monkeypatch.setenv("APP_ENV", "development")
    config, config_mod = reload_config_module(monkeypatch)
    assert isinstance(config, config_mod.DevConfig), (
        "Expected a DevConfig instance in development environment"
    )
    # In DevConfig, debug should be True and the auto validator should set log_level to "DEBUG".
    assert config.debug is True, "Expected debug to be True in DevConfig"
    assert config.log_level == "DEBUG", (
        "Expected log_level to be 'DEBUG' when debug is True"
    )


def test_log_level_auto_set():
    # Test that if log_level is empty, it is automatically set based on the debug flag.
    # For debug=True, log_level should become "DEBUG".
    conf_debug = AppConfig(debug=True, log_level="")
    assert conf_debug.log_level == "DEBUG", (
        "Expected log_level to be 'DEBUG' when debug is True and log_level is empty"
    )

    # For debug=False, log_level should become "INFO".
    conf_no_debug = AppConfig(debug=False, log_level="")
    assert conf_no_debug.log_level == "INFO", (
        "Expected log_level to be 'INFO' when debug is False and log_level is empty"
    )


def test_database_config_validator():
    # Test that DatabaseConfig raises a ValueError if database_url equals test_database_url.
    with pytest.raises(
        ValueError, match="database_url and test_database_url must be different"
    ):
        DatabaseConfig(
            database_url="postgresql+psycopg2://localhost/same_db",
            test_database_url="postgresql+psycopg2://localhost/same_db",
        )
