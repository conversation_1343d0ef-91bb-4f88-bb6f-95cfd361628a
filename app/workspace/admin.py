from typing import cast

from wtforms import SelectField

from app.admin import BaseModelView
from app.integrations.types import IntegrationSource
from app.workspace.models import (
    Environment,
    IntegrationConfig,
    OAuthToken,
    Organization,
    OrganizationMember,
)
from app.workspace.types import EnvironmentType, IntegrationType


class _WorkspaceModelView(BaseModelView):
    category = "Workspace"
    column_labels = {
        "environment.organization.name": "organization",
        "integration_config.environment.organization.name": "organization",
        "integration_type": "type",
    }


class OrganizationAdmin(_WorkspaceModelView, model=Organization):
    autodiscover_order = 10
    column_list = [
        Organization.id,
        Organization.name,
        Organization.domain,
        Organization.is_active,
    ]


class EnvironmentAdmin(_WorkspaceModelView, model=Environment):
    autodiscover_order = 20
    column_list = [
        Environment.id,
        OrganizationMember.organization,
        Environment.type,
    ]
    column_formatters = {
        Environment.organization: lambda m, _: cast("Environment", m).organization.name,
    }
    form_overrides = {"type": SelectField}
    form_args = {
        "type": {
            "choices": [(e.value, e.name) for e in EnvironmentType],
            "coerce": EnvironmentType,
        }
    }


class IntegrationConfigAdmin(_WorkspaceModelView, model=IntegrationConfig):
    autodiscover_order = 30
    column_list = [
        IntegrationConfig.id,
        "environment.organization.name",
        IntegrationConfig.source,
        IntegrationConfig.integration_type,
        IntegrationConfig.environment,
        IntegrationConfig.is_active,
    ]
    column_formatters = {
        IntegrationConfig.environment: lambda i, _: cast(
            "IntegrationConfig", i
        ).environment.type,
    }
    form_overrides = {"source": SelectField, "integration_type": SelectField}
    form_args = {
        "source": {
            "choices": [(e.value, e.name) for e in IntegrationSource],
            "coerce": IntegrationSource,
        },
        "integration_type": {
            "choices": [(e.value, e.name) for e in IntegrationType],
            "coerce": IntegrationType,
        },
    }


class OAuthTokenAdmin(_WorkspaceModelView, model=OAuthToken):
    autodiscover_order = 35
    name = "OAuth token"
    name_plural = "OAuth tokens"
    column_list = [
        OAuthToken.id,
        OAuthToken.user,
        OAuthToken.external_user_id,
        "integration_config.environment.organization.name",
        "integration",
        "environment",
    ]
    column_formatters = {
        "integration_config.environment.organization.name": lambda i, _: cast(
            "OAuthToken", i
        ).integration_config.environment.organization.name,
        OAuthToken.user: lambda i, _: cast("OAuthToken", i).user_id,
        OAuthToken.integration_config: lambda i, _: cast(
            "OAuthToken", i
        ).integration_config.source,
        "environment": lambda i, _: cast(
            "OAuthToken", i
        ).integration_config.environment.type.value,
        "integration": lambda i, _: cast(
            "OAuthToken", i
        ).integration_config.source.value,
    }


class OrganizationMemberAdmin(_WorkspaceModelView, model=OrganizationMember):
    autodiscover_order = 40
    name = "Member"
    name_plural = "Members"
    column_list = [
        OrganizationMember.id,
        OrganizationMember.organization,
        OrganizationMember.user,
    ]
    column_formatters = {
        OrganizationMember.user: lambda m, _: cast("OrganizationMember", m).user_id,
        OrganizationMember.organization: lambda m, _: cast(
            "OrganizationMember", m
        ).organization.name,
    }
