from uuid import UUID

from app.workspace.integrations.user_integrations import (
    UserIntegrations,
)
from app.workspace.schemas import UserCRMInfo


class CRMService:
    def __init__(self, user_id: UUID, user_integrations: UserIntegrations):
        self.user_id = user_id
        self.user_integrations = user_integrations

    def get_crm(self) -> UserCRMInfo | None:
        crm_provider, crm_user_id = (
            self.user_integrations.crm(),
            self.user_integrations.crm_user_id,
        )
        if not crm_provider or not crm_user_id:
            return None
        return UserCRMInfo(crm_name=crm_provider.source, crm_user_id=crm_user_id)
