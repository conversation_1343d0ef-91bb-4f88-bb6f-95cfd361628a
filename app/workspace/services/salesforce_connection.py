import uuid
from datetime import UTC, datetime, timedelta

from pydantic import BaseModel

from app.common.helpers.logger import get_logger
from app.common.oauth.flow_manager import OAuthFlowManager, OAuthFlowType
from app.core.database import Session
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
    IntegrationTokenNotFoundError,
)
from app.workspace.models import IntegrationConfig
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.oauth_token import OAuthTokenRepository
from app.workspace.schemas import OrgEnvironment, SalesforceCredentials

logger = get_logger()


class SalesforceTokenResponse(BaseModel):
    external_user_id: str
    external_org_id: str
    instance_url: str | None = None
    access_token: str
    expires_at: datetime


class SalesforceConnectionService:
    def __init__(
        self,
        db_session: Session,
        oauth_token_repo: OAuthTokenRepository,
        integration_cfg_repo: IntegrationConfigRepository,
        auth_url: str,
        token_url: str,
        redirect_uri: str,
        flow_type: OAuthFlowType,  # todo: replace with use_pkce
    ):
        self.db_session = db_session
        self.oauth_token_repo = oauth_token_repo
        self.integration_cfg_repo = integration_cfg_repo
        self.integration_source = IntegrationSource.SALESFORCE
        self.auth_url = auth_url
        self.token_url = token_url
        self.redirect_uri = redirect_uri
        self.flow_type = flow_type
        self.oauth_flow_manager = OAuthFlowManager()

    def generate_oauth_authorization_uri(
        self,
        user_id: uuid.UUID,
        environment: OrgEnvironment,
        scope: str = "refresh_token full",
    ) -> str:
        _, credentials = self._get_config_and_credentials(environment)

        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing Salesforce client_id and client_secret"
            )

        return self.oauth_flow_manager.generate_authorization_uri(
            uid=str(user_id),
            client_id=credentials.client_id,
            redirect_uri=self.redirect_uri,
            auth_url=self.auth_url,
            flow_type=self.flow_type,
            scope=scope,
        )

    async def process_oauth_callback(
        self,
        user_id: uuid.UUID,
        environment: OrgEnvironment,
        code: str,
        state: str,
    ) -> SalesforceTokenResponse:
        integration_config, credentials = self._get_config_and_credentials(environment)

        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing Salesforce client_id and client_secret"
            )

        token_data = await self.oauth_flow_manager.exchange_code_for_token(
            code=code,
            state=state,
            expected_uid=str(user_id),
            client_id=credentials.client_id,
            client_secret=credentials.client_secret,
            redirect_uri=self.redirect_uri,
            token_url=self.token_url,
        )

        salesforce_user_id = token_data["id"].split("/")[-1]
        salesforce_org_id = (
            token_data.get("org_id", "") or token_data["id"].split("/")[-2]
        )

        # Default fallback for access token expiration is 2 hours (7200 seconds)
        # This is the typical duration for Salesforce OAuth tokens, but may vary
        # If expires_in is missing in the token response, we use this conservative estimate
        expires_in = int(token_data.get("expires_in", 7200))
        now = datetime.now(UTC)
        expires_at = now + timedelta(seconds=expires_in)

        oauth_token = self.oauth_token_repo.get_by_user_and_integration(
            user_id=user_id, integration_config_id=integration_config.id
        )

        if oauth_token:
            oauth_token = self.oauth_token_repo.update(
                oauth_token.id,
                access_token=token_data.get("access_token"),
                refresh_token=token_data.get("refresh_token"),
                instance_url=token_data.get("instance_url"),
                scope=token_data.get("scope"),
                token_type=token_data.get("token_type"),
                expires_at=expires_at,
                last_refreshed_at=now,
                external_user_id=salesforce_user_id,
                external_org_id=salesforce_org_id,
            )
        else:
            oauth_token = self.oauth_token_repo.create(
                user_id=user_id,
                integration_config_id=integration_config.id,
                external_user_id=salesforce_user_id,
                external_org_id=salesforce_org_id,
                access_token=token_data.get("access_token"),
                refresh_token=token_data.get("refresh_token"),
                instance_url=token_data.get("instance_url"),
                scope=token_data.get("scope"),
                token_type=token_data.get("token_type"),
                expires_at=expires_at,
                last_refreshed_at=now,
            )
        self.db_session.commit()

        return SalesforceTokenResponse(
            external_user_id=oauth_token.external_user_id,
            external_org_id=oauth_token.external_org_id,
            instance_url=oauth_token.instance_url,
            access_token=oauth_token.access_token,
            expires_at=expires_at,
        )

    async def refresh_access_token(
        self,
        oauth_token_id: uuid.UUID,
        environment: OrgEnvironment,
    ) -> SalesforceTokenResponse:
        oauth_token = self.oauth_token_repo.get_by_id(oauth_token_id)
        if not oauth_token:
            raise IntegrationTokenNotFoundError()

        _, credentials = self._get_config_and_credentials(environment)

        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing Salesforce client_id and client_secret"
            )

        token_data = await self.oauth_flow_manager.refresh_access_token(
            refresh_token=oauth_token.refresh_token,
            client_id=credentials.client_id,
            client_secret=credentials.client_secret,
            token_url=self.token_url,
        )

        # Default fallback for access token expiration is 2 hours (7200 seconds)
        # This is the typical duration for Salesforce OAuth tokens, but may vary
        # If expires_in is missing in the token response, we use this conservative estimate
        expires_in = int(token_data.get("expires_in", 7200))
        now = datetime.now(UTC)
        expires_at = now + timedelta(seconds=expires_in)

        update_data = {
            "access_token": token_data.get("access_token"),
            "expires_at": expires_at,
            "last_refreshed_at": now,
        }

        if "instance_url" in token_data:
            update_data["instance_url"] = token_data.get("instance_url")

        if "refresh_token" in token_data:
            update_data["refresh_token"] = token_data.get("refresh_token")

        if "scope" in token_data:
            update_data["scope"] = token_data.get("scope")

        if "token_type" in token_data:
            update_data["token_type"] = token_data.get("token_type")

        updated_token = self.oauth_token_repo.update(oauth_token.id, **update_data)
        self.db_session.commit()

        return SalesforceTokenResponse(
            external_user_id=updated_token.external_user_id,
            external_org_id=updated_token.external_org_id,
            instance_url=updated_token.instance_url,
            access_token=updated_token.access_token,
            expires_at=expires_at,
        )

    def _get_config_and_credentials(
        self, environment: OrgEnvironment
    ) -> tuple[IntegrationConfig, SalesforceCredentials]:
        config = self.integration_cfg_repo.get_by_org_and_source(
            environment.organization_id, self.integration_source, environment.type
        )
        if not config:
            raise IntegrationConfigError(
                "No Salesforce integration configured for this organization"
            )
        return config, SalesforceCredentials.model_validate(config.credentials)
