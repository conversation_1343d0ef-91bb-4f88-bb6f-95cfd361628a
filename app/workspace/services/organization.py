import uuid
from uuid import UUID

from sqlalchemy.orm import Session

from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.schemas import OrganizationRead, OrgEnvironment
from app.workspace.types import EnvironmentType


class OrganizationService:
    def __init__(
        self,
        db_session: Session,
        org_repo: OrganizationRepository,
        env_repo: EnvironmentRepository,
    ):
        self.db_session = db_session
        self.org_repo = org_repo
        self.env_repo = env_repo

    def get_user_organization(self, user_id: UUID) -> OrganizationRead | None:
        org = self.org_repo.get_by_user_id(user_id)

        if org is None:
            return None

        return OrganizationRead.model_validate(org)

    def get_env(
        self, org_id: uuid.UUID, env_type: EnvironmentType
    ) -> OrgEnvironment | None:
        env = self.env_repo.get_by_org_id_and_type(org_id, env_type)

        if env is None:
            return None

        return OrgEnvironment.model_validate(env)
