from uuid import UUID

from app.common.helpers.logger import get_logger
from app.integrations.types import IntegrationSource
from app.workspace.models import IntegrationConfig, OAuthToken
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.oauth_token import OAuthTokenRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.types import CRM_SOURCES

logger = get_logger()


class IntegrationConfigService:
    def __init__(
        self,
        integration_cfg_repo: IntegrationConfigRepository,
        oauth_token_repo: OAuthTokenRepository,
    ):
        self.integration_cfg_repo = integration_cfg_repo
        self.oauth_token_repo = oauth_token_repo

    def get_integration_configs(
        self, environment: OrgEnvironment
    ) -> list[IntegrationConfig]:
        return self.integration_cfg_repo.get_all_by_org_id(
            org_id=environment.organization_id, env_type=environment.type
        )

    def get_integration_config(
        self,
        environment: OrgEnvironment,
        source: IntegrationSource,
    ) -> IntegrationConfig | None:
        configs = self.get_integration_configs(environment)
        config = next((config for config in configs if config.source is source), None)

        if not config:
            logger.warning(
                f"No integration config found for org {environment.organization_id}, source {source}"
            )

        return config

    def get_crm_config(self, environment: OrgEnvironment) -> IntegrationConfig | None:
        configs = self.get_integration_configs(environment)
        return next(
            (config for config in configs if config.source in CRM_SOURCES),
            None,
        )

    def get_oauth_token_for_user(
        self, integration_config_id: UUID, user_id: UUID
    ) -> OAuthToken | None:
        token = self.oauth_token_repo.get_by_user_and_integration(
            integration_config_id=integration_config_id, user_id=user_id
        )

        if not token:
            logger.debug(
                f"No OAuth token found for user {user_id}, integration {integration_config_id}"
            )

        return token
