import uuid
from collections.abc import Sequence

from sqlalchemy.orm import Session

from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.repositories.organization_member import OrganizationMemberRepository
from app.workspace.schemas import OrganizationMemberRead


class OrganizationTeamService:
    def __init__(
        self,
        db_session: Session,
        org_member_repo: OrganizationMemberRepository,
        org_repo: OrganizationRepository,
    ):
        self.db_session = db_session
        self.org_member_repo = org_member_repo
        self.org_repo = org_repo

    def get_team_user_ids(self, org_id: uuid.UUID) -> Sequence[uuid.UUID]:
        members = self.org_member_repo.get_by_organization_id(org_id)
        member_user_ids = [member.user_id for member in members]

        org = self.org_repo.get_by_id(org_id)
        if org and org.owner_id and org.owner_id not in member_user_ids:
            member_user_ids.append(org.owner_id)

        return member_user_ids

    def get_team_member(self, user_id: uuid.UUID) -> OrganizationMemberRead | None:
        member = self.org_member_repo.get_by_user_id(user_id)

        if not member:
            return None

        return OrganizationMemberRead.model_validate(member)
