from fastapi import APIRouter

from app.workspace.dependencies import AccountServiceDep
from app.workspace.schemas import AccountRead

router = APIRouter()


@router.get(
    "/accounts",
    response_model=list[AccountRead],
    name="get_accounts",
)
def get_accounts(service: AccountServiceDep):
    return service.get_accounts()


@router.get(
    "/sync_accounts",
    response_model=None,
    name="sync_accounts",
)
def sync_accounts(service: AccountServiceDep):
    return service.sync_accounts()
