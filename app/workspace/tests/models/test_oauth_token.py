# ruff: noqa: S106

import uuid
from datetime import UTC, datetime, timedelta

import pytest
from sqlalchemy.exc import IntegrityError

from app.auth.models import User
from app.integrations.types import IntegrationSource
from app.workspace.models import (
    Environment,
    IntegrationConfig,
    OAuthToken,
    Organization,
)
from app.workspace.types import EnvironmentType, IntegrationType


def test_oauth_token_unique_constraint(db_session):
    user = User(
        email="<EMAIL>",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
    )
    organization = Organization(
        name="Test Organization",
        domain="test.org",
        owner=user,
    )
    environment = Environment(
        organization=organization,
        type=EnvironmentType.PROD,
    )
    integration_config = IntegrationConfig(
        id=uuid.uuid4(),
        environment=environment,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
    )
    db_session.add_all([user, organization, environment, integration_config])
    db_session.commit()

    now = datetime.now(UTC)
    token1 = OAuthToken(
        user_id=user.id,
        integration_config_id=integration_config.id,
        external_user_id="ext_user_1",
        external_org_id="ext_org_1",
        access_token="access_token_1",
        refresh_token="refresh_token_1",
        expires_at=now + timedelta(hours=1),
        last_refreshed_at=now,
    )
    db_session.add(token1)
    db_session.commit()

    token2 = OAuthToken(
        user_id=user.id,
        integration_config_id=integration_config.id,
        external_user_id="ext_user_2",
        external_org_id="ext_org_2",
        access_token="access_token_2",
        refresh_token="refresh_token_2",
        expires_at=now + timedelta(hours=2),
        last_refreshed_at=now,
    )
    db_session.add(token2)

    with pytest.raises(IntegrityError) as excinfo:
        db_session.commit()

    assert "uq_user_integration_config" in str(excinfo.value)
