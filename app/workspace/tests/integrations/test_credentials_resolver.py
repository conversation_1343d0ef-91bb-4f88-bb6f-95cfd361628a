import uuid
from datetime import datetime

import pytest

from app.integrations.types import IntegrationSource
from app.workspace.integrations.credentials_resolver import (
    OrganizationCredentialsResolver,
    SimpleCredentialsResolver,
    UserCredentialsResolver,
    WorkspaceCredentials,
    WorkspaceSalesforceCredentials,
)
from app.workspace.models import OAuthToken
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


def create_mock_oauth_token() -> OAuthToken:
    token = OAuthToken()
    token.id = uuid.uuid4()
    token.access_token = "mock_access_token"
    token.instance_url = "https://test.salesforce.com"
    return token


def create_mock_integration_config() -> object:
    mock_config = type("MockIntegrationConfig", (), {})()
    mock_config.id = uuid.uuid4()
    mock_config.credentials = {"api_key": "test_key", "api_secret": "test_secret"}
    return mock_config


def test_workspace_credentials_secrets():
    secrets = {"api_key": "test_key", "api_secret": "test_secret"}
    credentials = WorkspaceCredentials(secrets)

    assert credentials.secrets == secrets
    assert credentials.refresh_token() is credentials


def test_workspace_salesforce_credentials_secrets(mocker, mock_environment):
    token = create_mock_oauth_token()
    connection_service = mocker.MagicMock(spec=SalesforceConnectionService)

    credentials = WorkspaceSalesforceCredentials(
        oauth_token=token,
        connection_service=connection_service,
        environment=mock_environment,
    )

    assert credentials.secrets == {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }


def test_workspace_salesforce_credentials_refresh_success(mocker, mock_environment):
    token = create_mock_oauth_token()
    connection_service = mocker.MagicMock(spec=SalesforceConnectionService)

    response = mocker.MagicMock()
    response.access_token = "refreshed_access_token"
    response.instance_url = "https://refreshed.salesforce.com"

    mocker.patch("asyncio.run", return_value=response)

    credentials = WorkspaceSalesforceCredentials(
        oauth_token=token,
        connection_service=connection_service,
        environment=mock_environment,
    )

    refreshed = credentials.refresh_token()

    assert refreshed.secrets == {
        "access_token": "refreshed_access_token",
        "instance_url": "https://refreshed.salesforce.com",
    }

    connection_service.refresh_access_token.assert_called_once_with(
        oauth_token_id=token.id,
        environment=mock_environment,
    )


def test_workspace_salesforce_credentials_refresh_failure(mocker, mock_environment):
    token = create_mock_oauth_token()
    connection_service = mocker.MagicMock(spec=SalesforceConnectionService)

    mocker.patch("asyncio.run", side_effect=Exception("Failed to refresh"))

    credentials = WorkspaceSalesforceCredentials(
        oauth_token=token,
        connection_service=connection_service,
        environment=mock_environment,
    )

    refreshed = credentials.refresh_token()
    assert refreshed is credentials

    assert refreshed.secrets == {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }


@pytest.mark.filterwarnings("ignore:coroutine.*was never awaited:RuntimeWarning")
def test_simple_credentials_resolver():
    secrets = {"api_key": "test_key", "api_secret": "test_secret"}
    credentials = WorkspaceCredentials(secrets)
    resolver = SimpleCredentialsResolver(credentials)

    result = resolver.get_credentials(source=IntegrationSource.SALESFORCE)
    assert result is credentials

    result = resolver.get_credentials(IntegrationSource.SLACK)
    assert result is credentials


def test_organization_resolver_get_credentials(mocker, mock_environment):
    mock_service = mocker.MagicMock()
    mock_config = create_mock_integration_config()

    mock_service.get_integration_config.return_value = mock_config

    resolver = OrganizationCredentialsResolver(
        environment=mock_environment,
        integration_config_service=mock_service,
    )

    credentials = resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == mock_config.credentials
    mock_service.get_integration_config.assert_called_once_with(
        environment=mock_environment,
        source=IntegrationSource.SALESFORCE,
    )


def test_organization_resolver_no_config_found(mocker, mock_environment):
    mock_service = mocker.MagicMock()
    mock_service.get_integration_config.return_value = None

    resolver = OrganizationCredentialsResolver(
        environment=mock_environment,
        integration_config_service=mock_service,
    )

    credentials = resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == {}


def test_user_resolver_with_salesforce_credentials(mocker, mock_environment):
    user_id = uuid.uuid4()
    mock_config_service = mocker.MagicMock()
    mock_sf_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)
    mock_config = create_mock_integration_config()
    mock_token = create_mock_oauth_token()

    mocker.patch("asyncio.run", return_value=None)

    mock_config_service.get_integration_config.return_value = mock_config
    mock_config_service.get_oauth_token_for_user.return_value = mock_token

    resolver = UserCredentialsResolver(
        environment=mock_environment,
        user_id=user_id,
        integration_config_service=mock_config_service,
        salesforce_connection_service=mock_sf_connection_service,
    )

    credentials = resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceSalesforceCredentials)
    assert credentials.secrets == {
        "access_token": "mock_access_token",
        "instance_url": "https://test.salesforce.com",
    }
    mock_config_service.get_oauth_token_for_user.assert_called_once_with(
        integration_config_id=mock_config.id, user_id=user_id
    )


def test_user_resolver_no_oauth_token(mocker, mock_environment):
    user_id = uuid.uuid4()
    mock_service = mocker.MagicMock()
    mock_sf_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)
    mock_config = create_mock_integration_config()

    mock_service.get_integration_config.return_value = mock_config
    mock_service.get_oauth_token_for_user.return_value = None

    resolver = UserCredentialsResolver(
        environment=mock_environment,
        user_id=user_id,
        integration_config_service=mock_service,
        salesforce_connection_service=mock_sf_connection_service,
    )

    credentials = resolver.get_credentials(IntegrationSource.SALESFORCE)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == {}


def test_user_resolver_non_oauth_source(mocker, mock_environment):
    user_id = uuid.uuid4()
    mock_service = mocker.MagicMock()
    mock_sf_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)
    mock_config = create_mock_integration_config()

    mock_service.get_integration_config.return_value = mock_config

    resolver = UserCredentialsResolver(
        environment=mock_environment,
        user_id=user_id,
        integration_config_service=mock_service,
        salesforce_connection_service=mock_sf_connection_service,
    )

    credentials = resolver.get_credentials(IntegrationSource.SLACK)

    assert isinstance(credentials, WorkspaceCredentials)
    assert credentials.secrets == mock_config.credentials
    mock_service.get_oauth_token_for_user.assert_not_called()
