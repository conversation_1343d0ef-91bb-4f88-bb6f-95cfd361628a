import uuid

import pytest

from app.auth.models import User
from app.workspace.models import (
    Organization,
    OrganizationMember,
)
from app.workspace.repositories.organization import OrganizationRepository


@pytest.fixture
def setup_user_and_orgs(db_session):
    user1 = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="User",
        last_name="One",
    )
    user2 = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="User",
        last_name="Two",
    )
    user3 = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="User",
        last_name="Three",
    )
    db_session.add_all([user1, user2, user3])
    db_session.flush()

    org1 = Organization(
        id=uuid.uuid4(),
        name="Org 1",
        domain="org1.com",
        is_active=True,
        owner_id=user1.id,
    )
    org2 = Organization(
        id=uuid.uuid4(),
        name="Org 2",
        domain="org2.com",
        is_active=True,
        owner_id=user2.id,
    )
    db_session.add_all([org1, org2])
    db_session.flush()

    member = OrganizationMember(
        user_id=user3.id,
        organization_id=org1.id,
    )
    db_session.add(member)
    db_session.commit()

    return {
        "user1": user1,
        "user2": user2,
        "user3": user3,
        "org1": org1,
        "org2": org2,
    }


def test_get_by_user_id_as_owner(db_session, setup_user_and_orgs):
    repo = OrganizationRepository(db_session)
    user1 = setup_user_and_orgs["user1"]
    org1 = setup_user_and_orgs["org1"]

    result = repo.get_by_user_id(user1.id)
    assert result is not None
    assert result.id == org1.id
    assert result.name == "Org 1"


def test_get_by_user_id_as_member(db_session, setup_user_and_orgs):
    repo = OrganizationRepository(db_session)
    user3 = setup_user_and_orgs["user3"]
    org1 = setup_user_and_orgs["org1"]

    result = repo.get_by_user_id(user3.id)
    assert result is not None
    assert result.id == org1.id
    assert result.name == "Org 1"


def test_get_by_user_id_with_no_org(db_session):
    user = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="No",
        last_name="Organization",
    )
    db_session.add(user)
    db_session.commit()

    repo = OrganizationRepository(db_session)

    result = repo.get_by_user_id(user.id)
    assert result is None
