import uuid

import pytest

from app.auth.models import User
from app.workspace.models import Organization, OrganizationMember
from app.workspace.repositories.organization_member import OrganizationMemberRepository


@pytest.fixture
def setup_organization_with_members(db_session):
    owner = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Owner",
        last_name="User",
    )
    member1 = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Member",
        last_name="One",
    )
    member2 = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Member",
        last_name="Two",
    )
    nonmember = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Non",
        last_name="Member",
    )
    db_session.add_all([owner, member1, member2, nonmember])
    db_session.flush()

    org = Organization(
        id=uuid.uuid4(),
        name="Test Organization",
        domain="test-org.com",
        is_active=True,
        owner_id=owner.id,
    )
    db_session.add(org)
    db_session.flush()

    org_member1 = OrganizationMember(
        user_id=member1.id,
        organization_id=org.id,
    )
    org_member2 = OrganizationMember(
        user_id=member2.id,
        organization_id=org.id,
    )
    db_session.add_all([org_member1, org_member2])
    db_session.commit()

    return {
        "owner": owner,
        "member1": member1,
        "member2": member2,
        "nonmember": nonmember,
        "org": org,
    }


def test_get_by_organization_id(db_session, setup_organization_with_members):
    org = setup_organization_with_members["org"]
    repo = OrganizationMemberRepository(db_session)

    members = repo.get_by_organization_id(org.id)

    assert len(members) == 2
    member_user_ids = [member.user_id for member in members]
    assert setup_organization_with_members["member1"].id in member_user_ids
    assert setup_organization_with_members["member2"].id in member_user_ids
    assert setup_organization_with_members["owner"].id not in member_user_ids


def test_get_by_organization_id_no_members(db_session):
    owner = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Solo",
        last_name="Owner",
    )
    db_session.add(owner)
    db_session.flush()

    empty_org = Organization(
        id=uuid.uuid4(),
        name="Empty Organization",
        domain="empty-org.com",
        is_active=True,
        owner_id=owner.id,
    )
    db_session.add(empty_org)
    db_session.commit()

    repo = OrganizationMemberRepository(db_session)

    members = repo.get_by_organization_id(empty_org.id)

    assert len(members) == 0
