import uuid
from datetime import UTC, datetime

import pytest

from app.auth.models import User
from app.integrations.types import IntegrationSource
from app.workspace.models import Environment, IntegrationConfig, Organization
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.types import EnvironmentType, IntegrationType


@pytest.fixture
def test_orgs(db_session):
    user_id = uuid.uuid4()
    user = User(
        id=user_id,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
    )
    db_session.add(user)

    org1 = Organization(
        id=uuid.uuid4(),
        name="Test Organization 1",
        domain="test1.com",
        is_active=True,
        owner_id=user_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    org2 = Organization(
        id=uuid.uuid4(),
        name="Test Organization 2",
        domain="test2.com",
        is_active=True,
        owner_id=user_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    db_session.add_all([org1, org2])
    db_session.flush()

    dev_env1 = Environment(
        id=uuid.uuid4(),
        organization_id=org1.id,
        type=EnvironmentType.SANDBOX,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    prod_env1 = Environment(
        id=uuid.uuid4(),
        organization_id=org1.id,
        type=EnvironmentType.PROD,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    dev_env2 = Environment(
        id=uuid.uuid4(),
        organization_id=org2.id,
        type=EnvironmentType.SANDBOX,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    db_session.add_all([dev_env1, prod_env1, dev_env2])
    db_session.flush()

    return {
        "org1": org1,
        "org2": org2,
        "dev_env1": dev_env1,
        "prod_env1": prod_env1,
        "dev_env2": dev_env2,
    }


def test_integration_config_get_all_by_org_id_multiple_orgs(db_session, test_orgs):
    config1 = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["dev_env1"].id,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={"username": "<EMAIL>"},
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config2 = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["prod_env1"].id,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={"username": "<EMAIL>"},
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config3 = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["dev_env2"].id,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={"username": "<EMAIL>"},
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config4 = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["dev_env1"].id,
        source=IntegrationSource.SLACK,
        integration_type=IntegrationType.MESSAGING,
        credentials={"slack_token": "inactive_key"},
        is_active=False,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    db_session.add_all([config1, config2, config3, config4])
    db_session.commit()

    repo = IntegrationConfigRepository(db_session)

    configs_org1 = repo.get_all_by_org_id(test_orgs["org1"].id)
    assert len(configs_org1) == 2
    config_ids = [config.id for config in configs_org1]
    assert config1.id in config_ids
    assert config2.id in config_ids
    assert config3.id not in config_ids
    assert config4.id not in config_ids

    configs_org2 = repo.get_all_by_org_id(test_orgs["org2"].id)
    assert len(configs_org2) == 1
    assert configs_org2[0].id == config3.id


def test_integration_config_get_all_by_org_id_with_different_sources(
    db_session, test_orgs
):
    config1 = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["dev_env1"].id,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={"username": "<EMAIL>"},
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config2 = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["prod_env1"].id,
        source=IntegrationSource.SLACK,
        integration_type=IntegrationType.MESSAGING,
        credentials={"slack_token": "slack_key"},
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    db_session.add_all([config1, config2])
    db_session.commit()

    repo = IntegrationConfigRepository(db_session)

    configs = repo.get_all_by_org_id(test_orgs["org1"].id)
    assert len(configs) == 2

    sources = {config.source for config in configs}
    assert IntegrationSource.SALESFORCE in sources
    assert IntegrationSource.SLACK in sources


def test_integration_config_get_all_by_org_id_active_filter(db_session, test_orgs):
    active_config = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["dev_env1"].id,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={"username": "<EMAIL>"},
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    inactive_config = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["dev_env2"].id,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={"username": "<EMAIL>"},
        is_active=False,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    db_session.add_all([active_config, inactive_config])
    db_session.commit()

    repo = IntegrationConfigRepository(db_session)

    configs = repo.get_all_by_org_id(test_orgs["org1"].id)

    assert len(configs) == 1
    assert configs[0].id == active_config.id
    assert configs[0].is_active is True


def test_integration_config_get_all_by_org_id_empty(db_session):
    user_id = uuid.uuid4()
    user = User(
        id=user_id,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
    )
    db_session.add(user)

    empty_org = Organization(
        id=uuid.uuid4(),
        name="Empty Organization",
        domain="empty.com",
        is_active=True,
        owner_id=user_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    db_session.add(empty_org)
    db_session.commit()

    repo = IntegrationConfigRepository(db_session)

    configs = repo.get_all_by_org_id(empty_org.id)

    assert len(configs) == 0
    assert configs == []


def test_integration_config_get_all_by_org_id_env_type_filter(db_session, test_orgs):
    config_sandbox = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["dev_env1"].id,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={"username": "<EMAIL>"},
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config_prod = IntegrationConfig(
        id=uuid.uuid4(),
        environment_id=test_orgs["prod_env1"].id,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={"username": "<EMAIL>"},
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    db_session.add_all([config_sandbox, config_prod])
    db_session.commit()

    repo = IntegrationConfigRepository(db_session)

    sandbox_configs = repo.get_all_by_org_id(
        test_orgs["org1"].id, env_type=EnvironmentType.SANDBOX
    )
    assert len(sandbox_configs) == 1
    assert sandbox_configs[0].id == config_sandbox.id
    assert sandbox_configs[0].environment.type == EnvironmentType.SANDBOX

    prod_configs = repo.get_all_by_org_id(
        test_orgs["org1"].id, env_type=EnvironmentType.PROD
    )
    assert len(prod_configs) == 1
    assert prod_configs[0].id == config_prod.id
    assert prod_configs[0].environment.type == EnvironmentType.PROD

    all_configs = repo.get_all_by_org_id(test_orgs["org1"].id)
    assert len(all_configs) == 2
    config_ids = [config.id for config in all_configs]
    assert config_sandbox.id in config_ids
    assert config_prod.id in config_ids
