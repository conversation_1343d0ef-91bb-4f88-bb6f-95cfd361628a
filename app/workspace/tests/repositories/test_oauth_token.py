# ruff: noqa: S106

import pytest

from app.auth.models import User
from app.integrations.types import IntegrationSource
from app.workspace.models import (
    Environment,
    IntegrationConfig,
    OAuthToken,
    Organization,
)
from app.workspace.repositories.oauth_token import OAuthTokenRepository
from app.workspace.types import EnvironmentType, IntegrationType


@pytest.fixture
def oauth_token_setup(db_session):
    repo = OAuthTokenRepository(db_session)

    user1 = User(
        email="<EMAIL>",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
    )
    user2 = User(
        email="<EMAIL>",
        first_name="<PERSON>",
        last_name="<PERSON>",
    )
    organization = Organization(
        name="Test Organization",
        domain="test.org",
        owner=user1,
    )
    environment = Environment(
        organization=organization,
        type=EnvironmentType.PROD,
    )
    integration_config1 = IntegrationConfig(
        environment=environment,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
    )
    integration_config2 = IntegrationConfig(
        environment=environment,
        source=IntegrationSource.SLACK,
        integration_type=IntegrationType.MESSAGING,
    )
    db_session.add_all(
        [
            user1,
            user2,
            organization,
            environment,
            integration_config1,
            integration_config2,
        ]
    )
    db_session.flush()

    return {
        "repo": repo,
        "user1": user1,
        "user2": user2,
        "integration_config1": integration_config1,
        "integration_config2": integration_config2,
        "db_session": db_session,
    }


def test_get_by_user_and_integration(oauth_token_setup):
    repo = oauth_token_setup["repo"]
    user1 = oauth_token_setup["user1"]
    user2 = oauth_token_setup["user2"]
    integration_config1 = oauth_token_setup["integration_config1"]
    integration_config2 = oauth_token_setup["integration_config2"]
    db_session = oauth_token_setup["db_session"]

    token1 = OAuthToken(
        user=user1,
        integration_config=integration_config1,
        external_user_id="ext_user_1",
        external_org_id="ext_org_1",
        access_token="access_token_1",
        refresh_token="refresh_token_1",
    )
    token2 = OAuthToken(
        user=user1,
        integration_config=integration_config2,
        external_user_id="ext_user_2",
        external_org_id="ext_org_2",
        access_token="access_token_2",
        refresh_token="refresh_token_2",
    )
    token3 = OAuthToken(
        user=user2,
        integration_config=integration_config1,
        external_user_id="ext_user_3",
        external_org_id="ext_org_3",
        access_token="access_token_3",
        refresh_token="refresh_token_3",
    )

    db_session.add_all([token1, token2, token3])
    db_session.flush()

    found_token = repo.get_by_user_and_integration(user1.id, integration_config1.id)
    assert found_token is not None
    assert found_token.external_user_id == "ext_user_1"
    assert found_token.access_token == "access_token_1"

    found_token = repo.get_by_user_and_integration(user2.id, integration_config1.id)
    assert found_token is not None
    assert found_token.external_user_id == "ext_user_3"
    assert found_token.access_token == "access_token_3"

    found_token = repo.get_by_user_and_integration(user2.id, integration_config2.id)
    assert found_token is None


def test_get_by_external_user_and_integration(oauth_token_setup):
    repo = oauth_token_setup["repo"]
    user1 = oauth_token_setup["user1"]
    user2 = oauth_token_setup["user2"]
    integration_config1 = oauth_token_setup["integration_config1"]
    integration_config2 = oauth_token_setup["integration_config2"]
    db_session = oauth_token_setup["db_session"]

    token1 = OAuthToken(
        user=user1,
        integration_config=integration_config1,
        external_user_id="ext_user_1",
        external_org_id="ext_org_1",
        access_token="access_token_1",
        refresh_token="refresh_token_1",
    )
    token2 = OAuthToken(
        user=user1,
        integration_config=integration_config2,
        external_user_id="ext_user_2",
        external_org_id="ext_org_2",
        access_token="access_token_2",
        refresh_token="refresh_token_2",
    )
    token3 = OAuthToken(
        user=user2,
        integration_config=integration_config1,
        external_user_id="ext_user_3",
        external_org_id="ext_org_3",
        access_token="access_token_3",
        refresh_token="refresh_token_3",
    )
    token4 = OAuthToken(
        user=user2,
        integration_config=integration_config2,
        external_user_id="ext_user_1",
        external_org_id="ext_org_4",
        access_token="access_token_4",
        refresh_token="refresh_token_4",
    )

    db_session.add_all([token1, token2, token3, token4])
    db_session.flush()

    found_token = repo.get_by_external_user_and_integration(
        integration_config1.id, "ext_user_1"
    )
    assert found_token is not None
    assert found_token.user_id == user1.id
    assert found_token.access_token == "access_token_1"

    found_token = repo.get_by_external_user_and_integration(
        integration_config1.id, "ext_user_3"
    )
    assert found_token is not None
    assert found_token.user_id == user2.id
    assert found_token.access_token == "access_token_3"

    found_token = repo.get_by_external_user_and_integration(
        integration_config2.id, "ext_user_1"
    )
    assert found_token is not None
    assert found_token.user_id == user2.id
    assert found_token.access_token == "access_token_4"

    found_token = repo.get_by_external_user_and_integration(
        integration_config1.id, "nonexistent_user"
    )
    assert found_token is None

    found_token = repo.get_by_external_user_and_integration(
        integration_config2.id, "ext_user_3"
    )
    assert found_token is None
