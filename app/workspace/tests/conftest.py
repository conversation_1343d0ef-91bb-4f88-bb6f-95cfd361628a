import pytest

from app.auth.dependencies import get_authenticated_user_id
from app.main import app
from app.workspace.dependencies import get_account_service
from app.workspace.schemas import AccountRead
from app.workspace.services.account import AccountService


@pytest.fixture(autouse=True)
def override_authenticated_user_id():
    def mock_get_authenticated_user_id():
        return "mocked_user_id"

    app.dependency_overrides[get_authenticated_user_id] = mock_get_authenticated_user_id
    yield
    app.dependency_overrides.pop(get_authenticated_user_id)


@pytest.fixture
def override_account_service_success(mocker):
    mock_service = mocker.MagicMock(spec=AccountService)
    account_1 = AccountRead(
        crm_id="1",
        crm_name="Account1",
    )
    account_2 = AccountRead(
        crm_id="2",
        crm_name="Account2",
    )
    mock_service.get_accounts.return_value = [account_1, account_2]
    app.dependency_overrides[get_account_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_account_service)
