import pytest

from app.main import app
from app.workspace.dependencies import get_crm_service
from app.workspace.schemas import UserCRMInfo


@pytest.fixture
def override_crm_service_success(mocker):
    mock_service = mocker.Mock()
    mock_service.get_crm.return_value = UserCRMInfo(
        crm_name="salesforce", crm_user_id="crm_user_id1"
    )
    app.dependency_overrides[get_crm_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_crm_service)


@pytest.fixture
def override_crm_service_none(mocker):
    mock_service = mocker.Mock()
    mock_service.get_crm.return_value = None
    app.dependency_overrides[get_crm_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_crm_service)


def test_get_user_crm_success(client, test_app, override_crm_service_success):
    url = test_app.url_path_for("get_user_crm")
    response = client.get(url)
    assert response.status_code == 200, response.text
    data = response.json()
    assert isinstance(data, dict)
    assert data["crm_name"] == "salesforce"
    assert data["crm_user_id"] == "crm_user_id1"
    override_crm_service_success.get_crm.assert_called_once()


def test_get_user_crm_none(client, test_app, override_crm_service_none):
    url = test_app.url_path_for("get_user_crm")
    response = client.get(url)
    assert response.status_code == 200, response.text
    data = response.json()
    assert data is None
    override_crm_service_none.get_crm.assert_called_once()
