import uuid

from app.workspace.schemas import UserCRMInfo
from app.workspace.services.crm import CRMService


def test_get_crm_with_valid_integration(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.source = "salesforce"

    mock_user_integrations = mocker.Mock(
        crm_user_id="crm_user_id1",
    )
    mock_user_integrations.crm.return_value = mock_crm_provider

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
    )

    result = service.get_crm()

    assert result is not None
    assert isinstance(result, UserCRMInfo)
    assert result.crm_name == "salesforce"
    assert result.crm_user_id == "crm_user_id1"
    mock_user_integrations.crm.assert_called_once()


def test_get_crm_without_crm_provider(mocker):
    mock_user_integrations = mocker.Mock(
        crm_user_id="crm_user_id1",
    )
    mock_user_integrations.crm.return_value = None

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
    )

    result = service.get_crm()

    assert result is None
    mock_user_integrations.crm.assert_called_once()


def test_get_crm_without_crm_user_id(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.source = "salesforce"

    mock_user_integrations = mocker.Mock(
        crm_user_id=None,
    )
    mock_user_integrations.crm.return_value = mock_crm_provider

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
    )

    result = service.get_crm()

    assert result is None
    mock_user_integrations.crm.assert_called_once()


def test_get_crm_without_both_provider_and_user_id(mocker):
    mock_user_integrations = mocker.Mock(
        crm_user_id=None,
    )
    mock_user_integrations.crm.return_value = None

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
    )

    result = service.get_crm()

    assert result is None
    mock_user_integrations.crm.assert_called_once()
