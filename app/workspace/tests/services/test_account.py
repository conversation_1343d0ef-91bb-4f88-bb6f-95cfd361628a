import uuid

from app.workspace.schemas import AccountRead
from app.workspace.services.account import AccountService


def test_get_accounts(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.list_account_access.return_value = [
        {
            "Id": "1",
            "Name": "Account1",
        },
        {
            "Id": "2",
            "Name": "Account2",
        },
    ]
    mock_user_integrations = mocker.Mock(
        crm_user_id="crm_user_id1",
    )
    mock_user_integrations.crm.return_value = mock_crm_provider

    user_id = uuid.uuid4()
    service = AccountService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
    )

    result = service.get_accounts()

    assert len(result) == 2
    assert all(isinstance(account, AccountRead) for account in result)

    ids = [account.crm_id for account in result]
    assert "1" in ids
    assert "2" in ids

    mock_crm_provider.list_account_access.assert_called_once_with(
        crm_user_id="crm_user_id1"
    )


def test_sync_accounts(mocker):
    mock_user_integrations = mocker.Mock()
    user_id = uuid.uuid4()

    service = AccountService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
    )

    service.sync_accounts()

    mock_user_integrations.sync_crm_accounts.assert_called_once()
