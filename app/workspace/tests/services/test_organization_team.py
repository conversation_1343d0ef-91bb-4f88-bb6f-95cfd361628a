import uuid
from types import SimpleNamespace

from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.repositories.organization_member import OrganizationMemberRepository
from app.workspace.services.organization_team import OrganizationTeamService


def test_get_team_user_ids_with_owner_as_member(mocker):
    org_id = uuid.uuid4()
    user1_id = uuid.uuid4()
    user2_id = uuid.uuid4()
    owner_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_db_session = mocker.Mock()

    mock_org = SimpleNamespace(
        id=org_id,
        owner_id=owner_id,
    )
    mock_org_repo.get_by_id.return_value = mock_org

    mock_members = [
        SimpleNamespace(user_id=user1_id),
        SimpleNamespace(user_id=user2_id),
        SimpleNamespace(user_id=owner_id),
    ]
    mock_org_member_repo.get_by_organization_id.return_value = mock_members

    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
    )

    result = service.get_team_user_ids(org_id)

    assert len(result) == 3
    assert user1_id in result
    assert user2_id in result
    assert owner_id in result
    mock_org_repo.get_by_id.assert_called_once_with(org_id)
    mock_org_member_repo.get_by_organization_id.assert_called_once_with(org_id)


def test_get_team_user_ids_with_owner_not_member(mocker):
    org_id = uuid.uuid4()
    user1_id = uuid.uuid4()
    user2_id = uuid.uuid4()
    owner_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_db_session = mocker.Mock()

    mock_org = SimpleNamespace(
        id=org_id,
        owner_id=owner_id,
    )
    mock_org_repo.get_by_id.return_value = mock_org

    mock_members = [
        SimpleNamespace(user_id=user1_id),
        SimpleNamespace(user_id=user2_id),
    ]
    mock_org_member_repo.get_by_organization_id.return_value = mock_members

    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
    )

    result = service.get_team_user_ids(org_id)

    assert len(result) == 3
    assert user1_id in result
    assert user2_id in result
    assert owner_id in result
    mock_org_repo.get_by_id.assert_called_once_with(org_id)
    mock_org_member_repo.get_by_organization_id.assert_called_once_with(org_id)


def test_get_team_user_ids_no_members(mocker):
    org_id = uuid.uuid4()
    owner_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_db_session = mocker.Mock()

    mock_org = SimpleNamespace(
        id=org_id,
        owner_id=owner_id,
    )
    mock_org_repo.get_by_id.return_value = mock_org

    mock_org_member_repo.get_by_organization_id.return_value = []

    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
    )

    result = service.get_team_user_ids(org_id)

    assert len(result) == 1
    assert owner_id in result
    mock_org_repo.get_by_id.assert_called_once_with(org_id)
    mock_org_member_repo.get_by_organization_id.assert_called_once_with(org_id)


def test_get_team_user_ids_no_org(mocker):
    org_id = uuid.uuid4()

    mock_org_member_repo = mocker.Mock(spec=OrganizationMemberRepository)
    mock_org_repo = mocker.Mock(spec=OrganizationRepository)
    mock_db_session = mocker.Mock()

    mock_org_repo.get_by_id.return_value = None

    mock_org_member_repo.get_by_organization_id.return_value = []

    service = OrganizationTeamService(
        db_session=mock_db_session,
        org_member_repo=mock_org_member_repo,
        org_repo=mock_org_repo,
    )

    result = service.get_team_user_ids(org_id)

    assert len(result) == 0
    mock_org_repo.get_by_id.assert_called_once_with(org_id)
    mock_org_member_repo.get_by_organization_id.assert_called_once_with(org_id)
