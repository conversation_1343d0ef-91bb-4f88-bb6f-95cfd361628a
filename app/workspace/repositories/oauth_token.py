import uuid

from app.common.orm.base_repository import BaseRepository
from app.workspace.models import OAuthToken


class OAuthTokenRepository(BaseRepository[OAuthToken]):
    def __init__(self, db_session):
        super().__init__(db_session, OAuthToken)

    def get_by_user_and_integration(
        self, user_id: uuid.UUID, integration_config_id: uuid.UUID
    ) -> OAuthToken | None:
        tokens = self._get_by_attrs(
            user_id=user_id, integration_config_id=integration_config_id
        )
        return tokens[0] if tokens else None

    def get_by_external_user_and_integration(
        self, integration_config_id: uuid.UUID, external_user_id: str
    ) -> OAuthToken | None:
        tokens = self._get_by_attrs(
            external_user_id=external_user_id,
            integration_config_id=integration_config_id,
        )
        return tokens[0] if tokens else None
