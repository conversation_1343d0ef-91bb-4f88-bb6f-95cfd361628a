from uuid import UUID

from sqlalchemy import select

from app.common.orm.base_repository import BaseRepository
from app.integrations.types import IntegrationSource
from app.workspace.models import Environment, IntegrationConfig, Organization
from app.workspace.types import EnvironmentType


class IntegrationConfigRepository(BaseRepository[IntegrationConfig]):
    def __init__(self, db_session):
        super().__init__(db_session, IntegrationConfig)

    def get_all_by_org_id(
        self, org_id: UUID, env_type: EnvironmentType | None = None
    ) -> list[IntegrationConfig]:
        stmt = (
            select(IntegrationConfig)
            .join(IntegrationConfig.environment)
            .join(Environment.organization)
            .where(IntegrationConfig.is_active.is_(True), Organization.id == org_id)
        )

        if env_type is not None:
            stmt = stmt.where(Environment.type == env_type)

        result = self.db_session.execute(stmt)

        return list(result.scalars().all())

    def get_by_org_and_source(
        self,
        org_id: UUID,
        source: IntegrationSource,
        env_type: EnvironmentType | None = None,
    ) -> IntegrationConfig | None:
        configs = self.get_all_by_org_id(org_id, env_type)
        for config in configs:
            if config.source == source:
                return config
        return None
