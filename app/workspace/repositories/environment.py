import uuid
from typing import Optional

from app.common.orm.base_repository import BaseRepository
from app.workspace.models.environment import Environment
from app.workspace.types import EnvironmentType


class EnvironmentRepository(BaseRepository[Environment]):
    def __init__(self, db_session):
        super().__init__(db_session, Environment)

    def get_by_org_id_and_type(
        self, org_id: uuid.UUID, env_type: EnvironmentType
    ) -> Optional[Environment]:
        environments = self._get_by_attrs(organization_id=org_id, type=env_type)
        return environments[0] if environments else None
