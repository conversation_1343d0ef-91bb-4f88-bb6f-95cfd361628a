import uuid

from app.common.orm.base_repository import BaseRepository
from app.workspace.models import OrganizationMember


class OrganizationMemberRepository(BaseRepository[OrganizationMember]):
    def __init__(self, db_session):
        super().__init__(db_session, OrganizationMember)

    def get_by_organization_id(self, org_id: uuid.UUID) -> list[OrganizationMember]:
        return self._get_by_attrs(organization_id=org_id)

    def get_by_user_id(self, user_id: uuid.UUID) -> OrganizationMember | None:
        res = self._get_by_attrs(user_id=user_id)
        return res[0] if res else None
