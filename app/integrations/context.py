import uuid
from collections.abc import Callable

from app.integrations.base.context import BaseContext
from app.integrations.base.credentials_resolver import ICredentialsResolver


class IntegrationContext(BaseContext):
    """
    Context object that encapsulates common dependencies needed by integration adapters.
    """

    def __init__(
        self,
        tenant_id: uuid.UUID,
        db_session_factory: Callable,
        credentials_resolver: ICredentialsResolver | None = None,
    ):
        super().__init__(tenant_id)
        self.db_session_factory = db_session_factory
        self.credentials_resolver = credentials_resolver


class IntegrationContextFactory:
    """
    Factory for creating integration contexts.
    """

    @classmethod
    def create_context(
        cls,
        tenant_id: uuid.UUID,
        db_session_factory: Callable,
        credentials_resolver: ICredentialsResolver | None = None,
    ) -> IntegrationContext:
        return IntegrationContext(
            tenant_id=tenant_id,
            db_session_factory=db_session_factory,
            credentials_resolver=credentials_resolver,
        )
