from collections.abc import Callable
from typing import Any, cast

from app.integrations.backends.crm.bulk_access_sync_handler import (
    BulkAccountAccessSyncHandler,
)
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.crm_adapter import Base<PERSON><PERSON>dapter
from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.context import IntegrationContext
from app.integrations.stores.pg_crm_store import PostgresCRMStore
from app.integrations.types import IntegrationSource


class CRMBackend(BaseCRMBackend):
    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseCRMAdapter],
        source: IntegrationSource,
    ):
        super().__init__(
            context=context,
            adapter_class=adapter_class,
            source=source,
        )

    @property
    def crm_store(self) -> PostgresCRMStore:
        if not hasattr(self, "_crm_store"):
            self._crm_store = PostgresCRMStore(
                tenant_id=self.context.tenant_id,
                source=self.source,
                session=self.context.db_session_factory(),
            )
        return self._crm_store

    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return self.get_adapter().get_opportunity(opportunity_id)

    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return self.get_adapter().update_opportunity(opportunity_id, fields)

    def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return self.get_adapter().list_opportunities_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    def get_account(self, account_id: str) -> dict[str, Any]:
        return self.get_adapter().get_account(account_id)

    def list_account_access(
        self, crm_user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        access_slice = self.crm_store.get_user_account_access(crm_user_id)
        paginated_accounts = access_slice.accounts[offset : offset + limit]
        accounts = []
        for account_access in paginated_accounts:
            accounts.append(
                {
                    "Id": account_access.account_id,
                    "Name": account_access.account_name,
                    "AccessType": account_access.access_type,
                    "AccessRole": account_access.access_role or "",
                }
            )

        return accounts

    def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        ctx = cast("IntegrationContext", self.context)
        credentials_resolver = cast("ICredentialsResolver", ctx.credentials_resolver)

        handler = BulkAccountAccessSyncHandler(
            tenant_id=self.context.tenant_id,
            source=self.source,
            credentials_resolver=credentials_resolver,
            db_session_factory=ctx.db_session_factory,
            adapter_factory=self.get_adapter_factory(),
            crm_store=self.crm_store,
        )
        return handler.execute(
            crm_user_ids=crm_user_ids,
            get_credentials_resolver=get_credentials_resolver,
            interval_seconds=interval_seconds,
            daemon_mode=daemon_mode,
        )
