import uuid

from app.common.helpers.logger import get_logger
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.base.crm_store import ICRMStore
from app.integrations.schemas import (
    CRMAccountAccessSlice,
    CRMAccountAccessSyncResult,
)

logger = get_logger()


class AccountAccessSynchronizer:
    def __init__(
        self,
        tenant_id: uuid.UUID,
        crm_store: ICRMStore,
        adapter: BaseCRMAdapter,
    ):
        self.tenant_id = tenant_id
        self.crm_store = crm_store
        self.adapter = adapter

    def sync_user_access(self, user_id: str) -> CRMAccountAccessSyncResult:
        try:
            logger.info(f"Starting access synchronization for user {user_id}")

            account_access_records = self.adapter.resolve_account_access(user_id)

            access_slice = CRMAccountAccessSlice(
                user_id=user_id,
                accounts=account_access_records,
            )

            access_stored, access_deleted = self.crm_store.store_account_access(
                access_slice
            )

            logger.info(
                f"Successfully synchronized access for user {user_id}: "
                f"{access_deleted} access entries before sync, "
                f"{access_stored} access entries after sync"
            )
            return CRMAccountAccessSyncResult(
                new_access_count=access_stored, old_access_count=access_deleted
            )

        except Exception:
            logger.exception(f"Failed to synchronize access for user {user_id}")
            raise
