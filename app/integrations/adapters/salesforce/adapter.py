from typing import Any

from app.integrations.adapters.salesforce.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.context import IntegrationContext
from app.integrations.schemas import CRMAccountAccessData
from app.integrations.types import IntegrationSource


class SalesforceAdapter(BaseCRMAdapter):
    def __init__(self, context: IntegrationContext):
        super().__init__(context)

        if context.credentials_resolver is None:
            raise ValueError("credentials_resolver is required for SalesforceAdapter")

        credentials = context.credentials_resolver.get_credentials(
            source=IntegrationSource.SALESFORCE
        )

        self._handler = SalesforceHandler(credentials=credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SALESFORCE

    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return self._handler.get_opportunity(opportunity_id)

    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return self._handler.update_opportunity(opportunity_id, fields)

    def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return self._handler.list_opportunities_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    def get_account(self, account_id: str) -> dict[str, Any]:
        return self._handler.get_account(account_id)

    def resolve_account_access(self, crm_user_id: str) -> list[CRMAccountAccessData]:
        return self._handler.resolve_account_access(salesforce_user_id=crm_user_id)
