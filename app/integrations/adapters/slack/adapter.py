import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.adapters.slack.client import Slack<PERSON><PERSON>
from app.integrations.adapters.slack.utils import (
    convert_slack_message_to_message_data,
)
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.messaging_adapter import BaseMessagingAdapter
from app.integrations.context import IntegrationContext
from app.integrations.schemas import ChannelDataSlice
from app.integrations.types import IntegrationSource

logger = get_logger()


class SlackAdapter(BaseMessagingAdapter):
    def __init__(self, context: IntegrationContext):
        super().__init__(context)

        if context.credentials_resolver is None:
            raise ValueError("credentials_resolver is required for SlackAdapter")

        self._client = self._create_client(context.credentials_resolver)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SLACK

    def _create_client(self, credentials_resolver: ICredentialsResolver) -> SlackClient:
        """Create a Slack client using the provided credentials resolver."""
        credentials = credentials_resolver.get_credentials(
            source=IntegrationSource.SLACK
        )

        if "slack_token" not in credentials.secrets:
            error_msg = "Slack token not found in credentials"
            logger.error(error_msg)
            raise ValueError(error_msg)

        return SlackClient(token=credentials.secrets["slack_token"])

    def get_channel_data(
        self,
        channel_id: str,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        batch_size: int = 100,
    ) -> ChannelDataSlice:
        try:
            # Get main channel messages
            messages = self._fetch_channel_messages(
                channel_id=channel_id,
                start_time=start_time,
                end_time=end_time,
                batch_size=batch_size,
            )

            # Get threaded replies
            all_messages = self._enrich_with_threaded_replies(
                channel_id=channel_id,
                messages=messages,
                batch_size=batch_size,
            )

            # Convert all messages to MessageData objects
            message_data_list = [
                convert_slack_message_to_message_data(msg, channel_id)
                for msg in all_messages
            ]

            # Sort by timestamp
            message_data_list.sort(key=lambda msg: msg.sent_at)

            return ChannelDataSlice(
                channel_id=channel_id,
                messages=message_data_list,
                from_time=start_time,
                to_time=end_time,
            )

        except Exception as e:
            logger.exception(f"Error getting channel data for {channel_id}")
            raise ValueError(f"Error getting channel data: {str(e)}")

    def _fetch_channel_messages(
        self,
        channel_id: str,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        batch_size: int = 100,
    ) -> list[dict[str, Any]]:
        """Internal method to fetch messages from a channel."""
        try:
            # Convert datetimes to Unix timestamps for Slack API
            oldest = str(start_time.timestamp())
            latest = str(end_time.timestamp())

            # Subtypes to filter out
            filtered_subtypes = [
                "thread_broadcast",  # Thread broadcast messages
                "channel_join",  # User joined the channel
                "channel_leave",  # User left the channel
                "channel_archive",  # Channel was archived
                "channel_unarchive",  # Channel was unarchived
            ]

            # Get the channel history
            messages = self._client.get_channel_history(
                channel_id=channel_id,
                limit=batch_size,
                oldest=oldest,
                latest=latest,
            )

            # Filter out unwanted message types
            filtered_messages = [
                msg for msg in messages if msg.get("subtype") not in filtered_subtypes
            ]
            filtered_count = len(messages) - len(filtered_messages)

            if filtered_count > 0:
                logger.info(
                    f"Filtered out {filtered_count} system messages (channel join/leave etc.)"
                )

            return filtered_messages

        except Exception as e:
            logger.exception(f"Error fetching messages from channel {channel_id}")
            raise ValueError(f"Error fetching messages: {str(e)}")

    def _enrich_with_threaded_replies(
        self, channel_id: str, messages: list[dict[str, Any]], batch_size: int = 100
    ) -> list[dict[str, Any]]:
        """Internal method to fetch thread replies and add them to the message list."""
        all_messages = messages.copy()
        threaded_messages = self._find_threaded_messages(messages)

        for parent in threaded_messages:
            thread_ts = parent.get("ts")
            logger.debug(f"Fetching replies for thread {thread_ts}")

            try:
                replies = self._client.get_thread_replies(
                    channel_id=channel_id,
                    thread_ts=thread_ts,
                    limit=batch_size,
                )

                for reply in replies:
                    # Add the reply if it's not already in the messages list
                    if not any(
                        msg.get("ts") == reply.get("ts") for msg in all_messages
                    ):
                        all_messages.append(reply)
            except Exception as e:
                logger.warning(
                    f"Error fetching replies for thread {thread_ts}: {str(e)}"
                )
                # Continue with other threads even if one fails

        return all_messages

    def _find_threaded_messages(
        self, messages: list[dict[str, Any]]
    ) -> list[dict[str, Any]]:
        """Internal method to find messages that have threads."""
        return self._client.find_threaded_messages(messages)

    def join_channel(self, channel_id: str) -> bool:
        try:
            self._client.join_channel(channel_id)
            return True
        except Exception as e:
            logger.warning(f"Error joining channel {channel_id}: {str(e)}")
            # Check if the error indicates we're already in the channel
            if "already_in_channel" in str(e):
                return True
            return False

    def get_channel_info(self, channel_id: str) -> dict[str, Any]:
        try:
            return self._client.get_channel_info(channel_id)
        except Exception as e:
            logger.exception(f"Error getting info for channel {channel_id}")
            raise ValueError(f"Error getting channel info: {str(e)}")
