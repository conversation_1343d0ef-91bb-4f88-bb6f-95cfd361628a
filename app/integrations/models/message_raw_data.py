import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from app.common.orm.types import StringEnum
from app.integrations.base.tenant_model import TenantModel
from app.integrations.types import IntegrationSource


class MessageRawData(TenantModel):
    __tablename__ = "message_raw_data"

    source: Mapped[IntegrationSource] = mapped_column(
        StringEnum(IntegrationSource), nullable=False
    )
    message_id: Mapped[str] = mapped_column(String, nullable=False)
    channel_id: Mapped[str] = mapped_column(String, nullable=False)
    raw_data: Mapped[str] = mapped_column(Text, nullable=False)
    hash: Mapped[str] = mapped_column(String, nullable=False)
    author: Mapped[str | None] = mapped_column(String, nullable=True)
    thread_id: Mapped[str | None] = mapped_column(String, nullable=True)
    parent_id: Mapped[str | None] = mapped_column(String, nullable=True)  # New column
    tombstone: Mapped[bool] = mapped_column(<PERSON>olean, default=False)
    sent_at: Mapped[datetime.datetime] = mapped_column(DateTime, nullable=False)
    last_edit_at: Mapped[datetime.datetime | None] = mapped_column(
        DateTime, nullable=True
    )

    __table_args__ = (
        UniqueConstraint(
            "tenant_id", "source", "message_id", name="uq_tenant_source_message"
        ),
    )

    @property
    def is_reply(self) -> bool:
        return self.parent_id is not None
