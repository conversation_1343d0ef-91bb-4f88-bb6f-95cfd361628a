from abc import ABC, abstractmethod
from typing import Any

from app.integrations.types import IntegrationSource


class ICredentials(ABC):
    @property
    @abstractmethod
    def secrets(self) -> dict[str, Any]:
        pass

    @abstractmethod
    def refresh_token(self) -> "ICredentials":
        pass


class ICredentialsResolver(ABC):
    @abstractmethod
    def get_credentials(self, source: IntegrationSource) -> ICredentials:
        pass
