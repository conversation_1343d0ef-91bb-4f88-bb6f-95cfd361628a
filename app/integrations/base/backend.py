import uuid
from abc import ABC, abstractmethod
from collections.abc import Callable

from app.integrations.base.adapter import Base<PERSON>dapter
from app.integrations.base.context import BaseContext
from app.integrations.types import BackendType, IntegrationSource


class BaseBackend[TContext: BaseContext, TAdapter: BaseAdapter](ABC):
    def __init__(
        self,
        context: TContext,
        adapter_class: type[TAdapter],
        source: IntegrationSource,
    ):
        self._context = context
        self._adapter_class = adapter_class
        self._source = source

    @property
    def context(self) -> TContext:
        return self._context

    @property
    def tenant_id(self) -> uuid.UUID:
        return self._context.tenant_id

    @property
    def source(self) -> IntegrationSource:
        return self._source

    @property
    def adapter_class(self) -> type[TAdapter]:
        return self._adapter_class

    def get_adapter(self) -> TAdapter:
        if not hasattr(self, "_adapter"):
            self._adapter = self._adapter_class(self.context)
        return self._adapter

    def get_adapter_factory(self) -> Callable[[TContext], TAdapter]:
        return lambda context: self._adapter_class(context)

    @property
    @abstractmethod
    def backend_type(self) -> BackendType:
        pass
