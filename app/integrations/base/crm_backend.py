from abc import ABC, abstractmethod
from collections.abc import Callable
from typing import Any

from app.integrations.base.backend import BaseBackend
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.types import BackendType


class BaseCRMBackend(BaseBackend, ABC):
    @property
    def backend_type(self) -> BackendType:
        return BackendType.CRM

    @abstractmethod
    def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    def get_account(self, account_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    def list_account_access(
        self, user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        pass
