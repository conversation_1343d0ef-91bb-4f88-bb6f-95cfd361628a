from abc import ABC, abstractmethod


class ICursorStore(ABC):
    @abstractmethod
    def get_position(self, cursor_id: str) -> int:
        """
        Gets the current position for a cursor
        Returns 0 if cursor doesn't exist
        """
        pass

    @abstractmethod
    def update_position(self, cursor_id: str, new_position: int) -> None:
        """
        Updates cursor position.
        Creates the cursor if it doesn't exist.
        """
        pass
