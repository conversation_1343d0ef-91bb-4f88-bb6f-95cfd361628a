import uuid

from sqlalchemy import Delete, Select, Update, delete, select
from sqlalchemy.orm import Session

from app.integrations.base.crm_store import ICRMStore
from app.integrations.models import CRMAccountAccess
from app.integrations.schemas import CRMAccountAccessData, CRMAccountAccessSlice
from app.integrations.types import IntegrationSource


class PostgresCRMStore(ICRMStore):
    def __init__(
        self,
        tenant_id: uuid.UUID,
        source: IntegrationSource,
        session: Session,
    ):
        self.tenant_id = tenant_id
        self.source = source
        self.session = session

    def clear_user_account_access(self, user_id: str) -> None:
        self._delete_user_account_access(user_id)
        self.session.commit()

    def store_account_access(
        self, access_slice: CRMAccountAccessSlice
    ) -> tuple[int, int]:
        user_id = access_slice.user_id

        deleted_count = self._delete_user_account_access(user_id)

        for account in access_slice.accounts:
            access = CRMAccountAccess(
                id=uuid.uuid4(),
                tenant_id=self.tenant_id,
                source=self.source,
                crm_account_id=account.account_id,
                crm_user_id=user_id,
                crm_account_name=account.account_name,
                crm_access_role=account.access_role,
                crm_access_type=account.access_type,
            )
            self.session.add(access)

        self.session.commit()

        return len(access_slice.accounts), deleted_count

    def get_user_account_access(self, user_id: str) -> CRMAccountAccessSlice:
        stmt = self._build_scoped_stmt(
            CRMAccountAccess,
            select(CRMAccountAccess),
            CRMAccountAccess.crm_user_id == user_id,
        )

        access_records = [
            CRMAccountAccessData(
                account_id=access.crm_account_id,
                account_name=access.crm_account_name,
                access_type=access.crm_access_type,
                access_role=access.crm_access_role,
            )
            for access in self.session.execute(stmt).scalars().all()
        ]

        return CRMAccountAccessSlice(
            user_id=user_id,
            accounts=access_records,
        )

    def _delete_user_account_access(self, user_id: str) -> int:
        delete_stmt = self._build_scoped_stmt(
            CRMAccountAccess,
            delete(CRMAccountAccess),
            CRMAccountAccess.crm_user_id == user_id,
        )
        result = self.session.execute(delete_stmt)
        return result.rowcount

    def _build_scoped_stmt(
        self,
        model: type[CRMAccountAccess],
        stmt: Select | Delete | Update,
        *conditions,
    ):
        return stmt.where(
            model.tenant_id == self.tenant_id,
            model.source == self.source,
            *conditions,
        )
