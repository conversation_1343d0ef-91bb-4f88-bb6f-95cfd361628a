from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session

from app.integrations.base.cursor_store import ICursorStore
from app.integrations.models.changelog_cursor import ChangelogCursor


class PostgresCursorStore(ICursorStore):
    def __init__(self, tenant_id: UUID, session: Session) -> None:
        self._tenant_id = tenant_id
        self._session = session

    def get_position(self, cursor_id: str) -> int:
        cursor = self._get_cursor(cursor_id)
        if cursor is None:
            return 0
        return cursor.cursor_position

    def update_position(self, cursor_id: str, new_position: int) -> None:
        cursor = self._get_cursor(cursor_id)

        if cursor is None:
            cursor = ChangelogCursor(
                cursor_id=cursor_id,
                tenant_id=self._tenant_id,
                cursor_position=new_position,
            )
            self._session.add(cursor)
        else:
            cursor.cursor_position = new_position

        self._session.commit()

    def _get_cursor(self, cursor_id: str) -> ChangelogCursor | None:
        stmt = select(ChangelogCursor).where(
            ChangelogCursor.tenant_id == self._tenant_id,
            ChangelogCursor.cursor_id == cursor_id,
        )

        return self._session.execute(stmt).scalar_one_or_none()
