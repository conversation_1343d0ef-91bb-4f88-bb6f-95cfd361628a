import uuid
from datetime import UTC, datetime

import pytest

from app.integrations.adapters.slack.adapter import SlackAdapter
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.schemas import ChannelDataSlice, MessageData
from app.integrations.types import IntegrationSource


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_credentials_resolver(mocker):
    mock_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_credentials = mocker.Mock()
    mock_credentials.secrets = {"slack_token": "xoxb-test-token"}
    mock_resolver.get_credentials.return_value = mock_credentials
    return mock_resolver


@pytest.fixture
def mock_context(mocker, tenant_id, db_session_factory, mock_credentials_resolver):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = tenant_id
    mock_context.db_session_factory = db_session_factory
    mock_context.credentials_resolver = mock_credentials_resolver
    return mock_context


@pytest.fixture
def mock_slack_client(mocker):
    mock_client = mocker.MagicMock()

    mock_client.get_channel_history.return_value = [
        {
            "ts": "1609459200.000001",
            "text": "Hello world",
            "user": "U12345",
            "channel": "C12345",
        }
    ]

    mock_client.get_thread_replies.return_value = [
        {
            "ts": "1609459300.000001",
            "text": "Reply to hello",
            "user": "U67890",
            "channel": "C12345",
            "thread_ts": "1609459200.000001",
        }
    ]

    mock_client.get_channel_info.return_value = {"id": "C12345", "name": "general"}

    mock_client.find_threaded_messages.return_value = [
        {
            "ts": "1609459200.000001",
            "thread_ts": "1609459200.000001",
        }
    ]

    return mock_client


@pytest.fixture
def slack_adapter(mock_context, mocker, mock_slack_client):
    mocker.patch.object(SlackAdapter, "_create_client", return_value=mock_slack_client)
    return SlackAdapter(context=mock_context)


def test_init(mock_context, mocker, mock_slack_client):
    mocker.patch.object(SlackAdapter, "_create_client", return_value=mock_slack_client)

    adapter = SlackAdapter(context=mock_context)

    assert adapter.context == mock_context
    assert adapter.tenant_id == mock_context.tenant_id
    assert adapter._client is mock_slack_client


def test_source(slack_adapter):
    assert slack_adapter.source == IntegrationSource.SLACK


def test_get_channel_data(slack_adapter):
    channel_id = "C12345"
    start_time = datetime(2021, 1, 1, tzinfo=UTC)
    end_time = datetime(2021, 1, 2, tzinfo=UTC)
    batch_size = 100

    result = slack_adapter.get_channel_data(
        channel_id=channel_id,
        start_time=start_time,
        end_time=end_time,
        batch_size=batch_size,
    )

    assert isinstance(result, ChannelDataSlice)
    assert result.channel_id == channel_id
    assert result.from_time == start_time
    assert result.to_time == end_time
    assert len(result.messages) == 2

    main_message = result.messages[0]
    assert isinstance(main_message, MessageData)
    assert main_message.message_id == "C12345:1609459200.000001"
    assert main_message.content == "Hello world"
    assert main_message.author == "U12345"

    reply_message = result.messages[1]
    assert isinstance(reply_message, MessageData)
    assert reply_message.message_id == "C12345:1609459300.000001"
    assert reply_message.content == "Reply to hello"
    assert reply_message.author == "U67890"
    assert reply_message.thread_id == "C12345:1609459200.000001"
    assert reply_message.parent_id == "C12345:1609459200.000001"


def test_join_channel(slack_adapter, mock_slack_client):
    channel_id = "C12345"

    result = slack_adapter.join_channel(channel_id=channel_id)

    mock_slack_client.join_channel.assert_called_once_with(channel_id)
    assert result is True


def test_get_channel_info(slack_adapter, mock_slack_client):
    channel_id = "C12345"

    result = slack_adapter.get_channel_info(channel_id=channel_id)

    mock_slack_client.get_channel_info.assert_called_once_with(channel_id)

    assert result["id"] == "C12345"
    assert result["name"] == "general"


def test_internal_fetch_channel_messages(slack_adapter, mock_slack_client):
    channel_id = "C12345"
    start_time = datetime(2021, 1, 1, tzinfo=UTC)
    end_time = datetime(2021, 1, 2, tzinfo=UTC)
    batch_size = 100

    result = slack_adapter._fetch_channel_messages(
        channel_id=channel_id,
        start_time=start_time,
        end_time=end_time,
        batch_size=batch_size,
    )

    oldest_timestamp = str(start_time.timestamp())
    latest_timestamp = str(end_time.timestamp())
    mock_slack_client.get_channel_history.assert_called_once_with(
        channel_id=channel_id,
        limit=batch_size,
        oldest=oldest_timestamp,
        latest=latest_timestamp,
    )

    assert len(result) == 1
    assert result[0]["text"] == "Hello world"


def test_internal_enrich_with_threaded_replies(slack_adapter, mock_slack_client):
    channel_id = "C12345"
    messages = [
        {
            "ts": "1609459200.000001",
            "thread_ts": "1609459200.000001",
            "text": "Hello world",
        }
    ]
    batch_size = 100

    result = slack_adapter._enrich_with_threaded_replies(
        channel_id=channel_id,
        messages=messages,
        batch_size=batch_size,
    )

    mock_slack_client.find_threaded_messages.assert_called_once_with(messages)

    mock_slack_client.get_thread_replies.assert_called_once_with(
        channel_id=channel_id,
        thread_ts="1609459200.000001",
        limit=batch_size,
    )

    assert len(result) == 2
    assert result[0]["ts"] == "1609459200.000001"
    assert result[1]["ts"] == "1609459300.000001"
    assert result[1]["text"] == "Reply to hello"


def test_internal_find_threaded_messages(slack_adapter, mock_slack_client):
    messages = [
        {"ts": "1609459200.000001", "thread_ts": "1609459200.000001"},
        {"ts": "1609459300.000001"},
    ]

    result = slack_adapter._find_threaded_messages(messages=messages)

    mock_slack_client.find_threaded_messages.assert_called_once_with(messages)

    assert len(result) == 1
    assert result[0]["ts"] == "1609459200.000001"
