import uuid

import pytest

from app.integrations.base.context import BaseContext
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import (
    IntegrationContext,
    IntegrationContextFactory,
)


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def mock_credentials_resolver(mocker):
    return mocker.Mock(spec=ICredentialsResolver)


def test_integration_context(tenant_id, db_session_factory, mock_credentials_resolver):
    context = IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
        credentials_resolver=mock_credentials_resolver,
    )

    assert context.tenant_id == tenant_id
    assert context.db_session_factory == db_session_factory
    assert context.credentials_resolver == mock_credentials_resolver
    assert isinstance(context, BaseContext)


def test_integration_context_without_credentials(tenant_id, db_session_factory):
    context = IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
    )

    assert context.tenant_id == tenant_id
    assert context.db_session_factory == db_session_factory
    assert context.credentials_resolver is None


def test_integration_context_factory(
    tenant_id, db_session_factory, mock_credentials_resolver
):
    context = IntegrationContextFactory.create_context(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
        credentials_resolver=mock_credentials_resolver,
    )

    assert isinstance(context, IntegrationContext)
    assert context.tenant_id == tenant_id
    assert context.db_session_factory == db_session_factory
    assert context.credentials_resolver == mock_credentials_resolver


def test_integration_context_factory_without_credentials(tenant_id, db_session_factory):
    context = IntegrationContextFactory.create_context(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
    )

    assert isinstance(context, IntegrationContext)
    assert context.tenant_id == tenant_id
    assert context.db_session_factory == db_session_factory
    assert context.credentials_resolver is None
