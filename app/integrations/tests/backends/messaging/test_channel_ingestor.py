import datetime

import pytest

from app.integrations.backends.messaging.channel_ingestor import (
    MessagingChannelIngestor,
)
from app.integrations.base.messaging_adapter import BaseMessagingAdapter
from app.integrations.schemas import (
    ChannelDataSlice,
    ChannelIngestionResult,
    MessageData,
)
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_adapter(mocker):
    adapter = mocker.Mock(spec=BaseMessagingAdapter)

    adapter.join_channel.return_value = True

    def get_channel_data(channel_id, start_time, end_time, *_args, **_kwargs):
        messages = [
            MessageData(
                message_id=f"{channel_id}:123.456",
                channel_id=channel_id,
                content="Hello",
                sent_at=datetime.datetime.fromtimestamp(
                    float("123.456"), tz=datetime.UTC
                ),
                author="U12345",
                thread_id=f"{channel_id}:123.456",
            ),
            MessageData(
                message_id=f"{channel_id}:123.789",
                channel_id=channel_id,
                content="World",
                sent_at=datetime.datetime.fromtimestamp(
                    float("123.789"), tz=datetime.UTC
                ),
                author="U67890",
                thread_id=None,
            ),
            MessageData(
                message_id=f"{channel_id}:124.123",
                channel_id=channel_id,
                content="Reply",
                sent_at=datetime.datetime.fromtimestamp(
                    float("124.123"), tz=datetime.UTC
                ),
                author="U12345",
                thread_id=f"{channel_id}:123.456",
                parent_id=f"{channel_id}:123.456",
            ),
        ]

        return ChannelDataSlice(
            channel_id=channel_id,
            messages=messages,
            from_time=start_time,
            to_time=end_time,
        )

    adapter.get_channel_data.side_effect = get_channel_data

    return adapter


@pytest.fixture
def mock_store(mocker):
    store = mocker.Mock()
    store.reconcile_channel_messages.return_value = mocker.Mock(
        inserts=5, updates=2, deletes=1
    )
    return store


@pytest.fixture
def channel_ingestor(mock_store, mock_adapter):
    return MessagingChannelIngestor(
        store=mock_store,
        source=IntegrationSource.SLACK,
        adapter=mock_adapter,
        batch_size=100,
    )


def test_ingest_channel_success(channel_ingestor):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    result = channel_ingestor.ingest_channel(channel_id, start_time, end_time)

    assert isinstance(result, ChannelIngestionResult)
    assert result.messages_count == 3
    assert result.inserts == 5
    assert result.updates == 2
    assert result.deletes == 1

    channel_ingestor.adapter.join_channel.assert_called_once_with(channel_id)
    channel_ingestor.adapter.get_channel_data.assert_called_once_with(
        channel_id=channel_id,
        start_time=start_time,
        end_time=end_time,
        batch_size=100,
    )

    channel_ingestor.store.reconcile_channel_messages.assert_called_once()
    call_args = channel_ingestor.store.reconcile_channel_messages.call_args[0]
    channel_slice = call_args[0]
    assert len(channel_slice.messages) == 3
    assert all(isinstance(msg, MessageData) for msg in channel_slice.messages)


def test_ingest_channel_messages_sorted(channel_ingestor, mocker):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    def get_unsorted_data(channel_id, start_time, end_time, *_args, **_kwargs):
        messages = [
            MessageData(
                message_id=f"{channel_id}:123.789",
                channel_id=channel_id,
                content="Later message",
                sent_at=datetime.datetime.fromtimestamp(
                    float("123.789"), tz=datetime.UTC
                ),
                author="U67890",
                thread_id=None,
            ),
            MessageData(
                message_id=f"{channel_id}:123.456",
                channel_id=channel_id,
                content="Earlier message",
                sent_at=datetime.datetime.fromtimestamp(
                    float("123.456"), tz=datetime.UTC
                ),
                author="U12345",
                thread_id=f"{channel_id}:123.456",
            ),
            MessageData(
                message_id=f"{channel_id}:123.900",
                channel_id=channel_id,
                content="Late reply",
                sent_at=datetime.datetime.fromtimestamp(
                    float("123.900"), tz=datetime.UTC
                ),
                author="U67890",
                thread_id=f"{channel_id}:123.456",
                parent_id=f"{channel_id}:123.456",
            ),
            MessageData(
                message_id=f"{channel_id}:123.500",
                channel_id=channel_id,
                content="Early reply",
                sent_at=datetime.datetime.fromtimestamp(
                    float("123.500"), tz=datetime.UTC
                ),
                author="U12345",
                thread_id=f"{channel_id}:123.456",
                parent_id=f"{channel_id}:123.456",
            ),
        ]

        return ChannelDataSlice(
            channel_id=channel_id,
            messages=sorted(messages, key=lambda m: m.sent_at),
            from_time=start_time,
            to_time=end_time,
        )

    channel_ingestor.adapter.get_channel_data.side_effect = get_unsorted_data

    reconcile_spy = mocker.spy(channel_ingestor.store, "reconcile_channel_messages")

    channel_ingestor.ingest_channel(channel_id, start_time, end_time)

    assert reconcile_spy.call_count == 1
    channel_slice = reconcile_spy.call_args[0][0]

    message_timestamps = [msg.sent_at for msg in channel_slice.messages]
    assert message_timestamps == sorted(message_timestamps)

    expected_contents = [
        "Earlier message",
        "Early reply",
        "Later message",
        "Late reply",
    ]
    assert [msg.content for msg in channel_slice.messages] == expected_contents


def test_ingest_channel_error_fetch(channel_ingestor):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    channel_ingestor.adapter.get_channel_data.side_effect = Exception("API error")

    with pytest.raises(RuntimeError, match="Failed to ingest channel"):
        channel_ingestor.ingest_channel(channel_id, start_time, end_time)

    channel_ingestor.adapter.join_channel.assert_called_once_with(channel_id)
    channel_ingestor.adapter.get_channel_data.assert_called_once()


def test_join_channel_success(channel_ingestor):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    channel_ingestor.ingest_channel(channel_id, start_time, end_time)

    channel_ingestor.adapter.join_channel.assert_called_once_with(channel_id)


def test_join_channel_warning(channel_ingestor):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    channel_ingestor.adapter.join_channel.return_value = False

    channel_ingestor.ingest_channel(channel_id, start_time, end_time)

    channel_ingestor.adapter.join_channel.assert_called_once_with(channel_id)


def test_join_channel_error(channel_ingestor):
    channel_id = "C12345"
    start_time = datetime.datetime.now(datetime.UTC) - datetime.timedelta(days=1)
    end_time = datetime.datetime.now(datetime.UTC)

    channel_ingestor.adapter.join_channel.side_effect = Exception(
        "Error joining channel"
    )

    with pytest.raises(RuntimeError):
        channel_ingestor.ingest_channel(channel_id, start_time, end_time)

    channel_ingestor.adapter.join_channel.assert_called_once_with(channel_id)
