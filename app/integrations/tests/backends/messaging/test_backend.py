import uuid
from datetime import UTC, datetime

import pytest

from app.integrations.backends.messaging.backend import MessagingBackend
from app.integrations.backends.messaging.ingest_runner import MessagingIngestRunner
from app.integrations.backends.messaging.process_runner import MessagingProcessRunner
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.schemas import DocumentData
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_context(mocker):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = uuid.uuid4()
    mock_context.db_session_factory = lambda: None
    mock_context.credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    return mock_context


@pytest.fixture
def mock_adapter_class(mocker):
    mock_class = mocker.Mock()
    return mock_class


@pytest.fixture
def messaging_backend(mock_context, mock_adapter_class):
    return MessagingBackend(
        context=mock_context,
        adapter_class=mock_adapter_class,
        source=IntegrationSource.SLACK,
    )


def test_init(mock_context, mock_adapter_class):
    backend = MessagingBackend(
        context=mock_context,
        adapter_class=mock_adapter_class,
        source=IntegrationSource.SLACK,
    )

    assert backend.context == mock_context
    assert backend.adapter_class == mock_adapter_class
    assert backend.source == IntegrationSource.SLACK
    assert backend.tenant_id == mock_context.tenant_id

    assert isinstance(backend.ingest_runner, MessagingIngestRunner)
    assert isinstance(backend.process_runner, MessagingProcessRunner)


def test_search_channel_messages(messaging_backend, mocker):
    mock_document_store = mocker.patch(
        "app.integrations.backends.messaging.backend.PostgresDocumentStore"
    )
    mock_document_store.return_value.find_similar_documents.return_value = [
        (
            DocumentData(
                id="doc1",
                content="test content",
                source_timestamp=datetime.now(UTC),
                tags={"tag1", "tag2", "channel_id:C123"},
            ),
            0.95,
        )
    ]
    mock_embedder = mocker.patch(
        "app.integrations.backends.messaging.backend.NoopEmbedder"
    )
    mock_embedder.return_value.embed_text.return_value = [0.1, 0.2, 0.3]

    channel_id = "C123"
    query = "test query"
    limit = 10

    result = messaging_backend.search_channel_messages(
        channel_id=channel_id, query=query, limit=limit
    )

    mock_embedder.return_value.embed_text.assert_called_once_with(query)

    mock_document_store.return_value.find_similar_documents.assert_called_once_with(
        embedding=[0.1, 0.2, 0.3],
        limit=limit,
        tag_filter=f"channel_id:{channel_id}",
    )

    assert len(result) == 1
    assert result[0][0].id == "doc1"
    assert result[0][0].content == "test content"
    assert result[0][1] == 0.95


def test_start_channel_ingestion(messaging_backend, mocker):
    mock_ingest_runner = mocker.Mock(spec=MessagingIngestRunner)
    mock_ingest_runner.run.return_value = {"status": "success"}
    messaging_backend.ingest_runner = mock_ingest_runner

    channel_ids = ["C123", "C456"]
    interval_seconds = 600
    lookback_days = 14
    batch_size = 200
    daemon_mode = True
    sequential_execution = True

    result = messaging_backend.start_channel_ingestion(
        channel_ids=channel_ids,
        interval_seconds=interval_seconds,
        lookback_days=lookback_days,
        batch_size=batch_size,
        daemon_mode=daemon_mode,
        sequential_execution=sequential_execution,
    )

    mock_ingest_runner.run.assert_called_once_with(
        channel_ids=channel_ids,
        interval_seconds=interval_seconds,
        lookback_days=lookback_days,
        batch_size=batch_size,
        daemon_mode=daemon_mode,
        sequential_execution=sequential_execution,
    )

    assert result == {"status": "success"}


def test_start_channel_processing(messaging_backend, mocker):
    mock_process_runner = mocker.Mock(spec=MessagingProcessRunner)
    mock_process_runner.run.return_value = {"status": "success"}
    messaging_backend.process_runner = mock_process_runner

    channel_ids = ["C123", "C456"]
    interval_seconds = 600
    batch_size = 200
    daemon_mode = True
    sequential_execution = True

    result = messaging_backend.start_channel_processing(
        channel_ids=channel_ids,
        interval_seconds=interval_seconds,
        batch_size=batch_size,
        daemon_mode=daemon_mode,
        sequential_execution=sequential_execution,
    )

    mock_process_runner.run.assert_called_once_with(
        channel_ids=channel_ids,
        interval_seconds=interval_seconds,
        batch_size=batch_size,
        daemon_mode=daemon_mode,
        sequential_execution=sequential_execution,
    )

    assert result == {"status": "success"}
