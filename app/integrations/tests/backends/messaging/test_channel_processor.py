import datetime

import pytest

from app.integrations.backends.messaging.channel_processor import (
    MessagingChannelProcessor,
)
from app.integrations.processors.readers.channel_changelog_reader import (
    MessageChangeSet,
)
from app.integrations.schemas import ChannelProcessingResult, MessageChangelogData


@pytest.fixture
def channel_processor(mocker):
    channel_changelog_reader = mocker.Mock()
    document_indexer = mocker.Mock()

    mock_changes = [
        MessageChangelogData(
            cursor_id=1,
            message_id="msg1",
            operation="UPDATE",
            channel_id="C12345",
            created_at=datetime.datetime.now(),
        ),
        MessageChangelogData(
            cursor_id=2,
            message_id="msg2",
            operation="INSERT",
            channel_id="C12345",
            created_at=datetime.datetime.now(),
        ),
    ]
    changeset = MessageChangeSet(
        channel_id="C12345",
        consumer_id="test_processor",
        changes=mock_changes,
    )
    channel_changelog_reader.get_next_changes.return_value = changeset

    document_changeset = mocker.Mock()
    document_changeset.documents_to_invalidate = ["doc1", "doc2"]
    document_changeset.documents_to_rebuild = ["doc3", "doc4"]
    document_indexer.determine_document_changes.return_value = document_changeset
    document_indexer.generate_document.return_value = {"generated": True}

    processor = MessagingChannelProcessor(
        channel_changelog_reader=channel_changelog_reader,
        document_indexer=document_indexer,
        batch_size=100,
        consumer_id="test_processor",
    )

    return processor


def test_process_channel_success(channel_processor):
    channel_id = "C12345"
    result = channel_processor.process_channel(channel_id)

    assert isinstance(result, ChannelProcessingResult)
    assert result.processed_changes == 2
    assert result.regenerated_documents == 2
    assert result.deleted_documents == 2

    channel_processor.channel_changelog_reader.get_next_changes.assert_called_once_with(
        channel_id="C12345",
        consumer_id="test_processor",
        batch_size=100,
    )
    channel_processor.document_indexer.determine_document_changes.assert_called_once()
    assert channel_processor.document_indexer.invalidate_document.call_count == 2
    assert channel_processor.document_indexer.generate_document.call_count == 2
    channel_processor.channel_changelog_reader.ack.assert_called_once()


def test_process_channel_no_changes(channel_processor):
    empty_changeset = MessageChangeSet(
        channel_id="C12345", consumer_id="test_processor", changes=[]
    )
    channel_processor.channel_changelog_reader.get_next_changes.return_value = (
        empty_changeset
    )

    result = channel_processor.process_channel("C12345")

    assert isinstance(result, ChannelProcessingResult)
    assert result.processed_changes == 0
    assert result.regenerated_documents == 0
    assert result.deleted_documents == 0

    channel_processor.channel_changelog_reader.get_next_changes.assert_called_once()
    channel_processor.document_indexer.determine_document_changes.assert_not_called()
    channel_processor.document_indexer.invalidate_document.assert_not_called()
    channel_processor.document_indexer.generate_document.assert_not_called()
    channel_processor.channel_changelog_reader.ack.assert_not_called()


def test_process_channel_generate_none(channel_processor):
    channel_processor.document_indexer.generate_document.return_value = None

    result = channel_processor.process_channel("C12345")

    assert result.processed_changes == 2
    assert result.regenerated_documents == 0  # None of the documents were regenerated
    assert result.deleted_documents == 2

    channel_processor.document_indexer.determine_document_changes.assert_called_once()
    assert channel_processor.document_indexer.invalidate_document.call_count == 2
    assert channel_processor.document_indexer.generate_document.call_count == 2
    channel_processor.channel_changelog_reader.ack.assert_called_once()


def test_process_channel_exception_propagation(channel_processor):
    channel_processor.document_indexer.determine_document_changes.side_effect = (
        Exception("Processing error")
    )

    with pytest.raises(Exception, match="Processing error"):
        channel_processor.process_channel("C12345")

    channel_processor.channel_changelog_reader.get_next_changes.assert_called_once()
    channel_processor.document_indexer.determine_document_changes.assert_called_once()
    channel_processor.channel_changelog_reader.ack.assert_not_called()


def test_custom_consumer_id(mocker):
    channel_changelog_reader = mocker.Mock()
    document_indexer = mocker.Mock()
    custom_consumer_id = "custom_consumer_123"

    changeset = MessageChangeSet(
        channel_id="C12345",
        consumer_id=custom_consumer_id,
        changes=[],
    )
    channel_changelog_reader.get_next_changes.return_value = changeset

    processor = MessagingChannelProcessor(
        channel_changelog_reader=channel_changelog_reader,
        document_indexer=document_indexer,
        batch_size=100,
        consumer_id=custom_consumer_id,
    )

    processor.process_channel("C12345")

    channel_changelog_reader.get_next_changes.assert_called_once_with(
        channel_id="C12345",
        consumer_id=custom_consumer_id,
        batch_size=100,
    )
