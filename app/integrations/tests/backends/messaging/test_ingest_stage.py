import datetime
import uuid

import pytest

from app.common.pipeline.base_stage import BaseStage
from app.integrations.backends.messaging.channel_ingestor import (
    MessagingChannelIngestor,
)
from app.integrations.backends.messaging.ingest_stage import MessagingIngestStage
from app.integrations.schemas import Channel<PERSON>nges<PERSON>R<PERSON>ult
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_ingestor(mocker):
    mock_ingestor = mocker.Mock(spec=MessagingChannelIngestor)
    type(mock_ingestor).source = mocker.PropertyMock(
        return_value=IntegrationSource.SLACK
    )
    return mock_ingestor


@pytest.fixture
def ingest_stage(mocker, mock_ingestor):
    channel_ids = ["C123", "C456"]
    tenant_id = uuid.uuid4()
    session = mocker.Mock()

    stage = MessagingIngestStage(
        tenant_id=tenant_id,
        source=IntegrationSource.SLACK,
        db_session=session,
        ingestor=mock_ingestor,
        channel_ids=channel_ids,
        lookback_days=3,
        interval_seconds=60,
    )

    return stage


def test_init(mocker, mock_ingestor):
    tenant_id = uuid.uuid4()
    channel_ids = ["C123", "C456"]
    session = mocker.Mock()
    source = IntegrationSource.SLACK

    stage = MessagingIngestStage(
        tenant_id=tenant_id,
        source=source,
        db_session=session,
        ingestor=mock_ingestor,
        channel_ids=channel_ids,
        lookback_days=5,
        interval_seconds=120,
        stage_id="test_stage",
    )

    assert stage.tenant_id == tenant_id
    assert stage.source == source
    assert stage.db_session == session
    assert stage.ingestor == mock_ingestor
    assert stage.channel_ids == channel_ids
    assert stage.lookback_days == 5
    assert stage.interval_seconds == 120
    assert stage.stage_id == "test_stage"


def test_execute_once_success(ingest_stage, mock_ingestor):
    channel_ids = ingest_stage.channel_ids

    result1 = ChannelIngestionResult(
        messages_count=10,
        inserts=7,
        updates=2,
        deletes=1,
        from_time=datetime.datetime(2023, 1, 1),
        to_time=datetime.datetime(2023, 1, 2),
    )
    result2 = ChannelIngestionResult(
        messages_count=20,
        inserts=15,
        updates=3,
        deletes=2,
        from_time=datetime.datetime(2023, 1, 1),
        to_time=datetime.datetime(2023, 1, 2),
    )
    mock_ingestor.ingest_channel.side_effect = [result1, result2]

    result = ingest_stage.execute_once()

    assert result["status"] == "success"
    assert result["channels_processed"] == 2
    assert result["messages_ingested"] == 30

    for i, channel_id in enumerate(channel_ids):
        channel_result = result["channels"].get(channel_id)
        assert channel_result is not None
        assert channel_result["status"] == "success"

        expected_messages = 10 if i == 0 else 20
        assert channel_result["messages_count"] == expected_messages

    assert ingest_stage.metrics["total_runs"] == 1
    assert ingest_stage.metrics["channels_processed"] == 2
    assert ingest_stage.metrics["messages_ingested"] == 30
    assert ingest_stage.metrics["successful_runs"] == 1
    assert ingest_stage.metrics["errors_count"] == 0

    assert mock_ingestor.ingest_channel.call_count == 2


def test_execute_once_partial(ingest_stage, mock_ingestor):
    def fake_ingest_channel(channel_id, *_, **__):
        if channel_id == "C456":
            raise Exception("Execute error")
        return ChannelIngestionResult(
            messages_count=10,
            inserts=7,
            updates=2,
            deletes=1,
            from_time=datetime.datetime(2023, 1, 1),
            to_time=datetime.datetime(2023, 1, 2),
        )

    mock_ingestor.ingest_channel.side_effect = fake_ingest_channel

    result = ingest_stage.execute_once()

    assert result["status"] == "partial"
    assert result["channels_processed"] == 1
    assert result["messages_ingested"] == 10

    channel_res_ok = result["channels"].get("C123")
    assert channel_res_ok is not None
    assert channel_res_ok["status"] == "success"
    assert channel_res_ok["messages_count"] == 10

    channel_res_err = result["channels"].get("C456")
    assert channel_res_err is not None
    assert channel_res_err["status"] == "error"
    assert "Execute error" in channel_res_err["error"]

    assert ingest_stage.metrics["total_runs"] == 1
    assert ingest_stage.metrics["channels_processed"] == 1
    assert ingest_stage.metrics["messages_ingested"] == 10
    assert ingest_stage.metrics["successful_runs"] == 0
    assert ingest_stage.metrics["errors_count"] == 1


def test_get_status(ingest_stage, mocker):
    dummy_base_status = {"base": True}
    mocker.patch.object(BaseStage, "get_status", return_value=dummy_base_status)

    status = ingest_stage.get_status()

    assert status["base"] is True
    assert status["tenant_id"] == str(ingest_stage.tenant_id)
    assert status["source"] == str(ingest_stage.source)
    assert status["channels_count"] == len(ingest_stage.channel_ids)
    assert status["interval_seconds"] == ingest_stage.interval_seconds
    assert status["lookback_days"] == ingest_stage.lookback_days
    assert "metrics" in status
