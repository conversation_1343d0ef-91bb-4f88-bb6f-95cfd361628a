import datetime
import uuid

import numpy as np
import pytest

from app.integrations.models.document import Document
from app.integrations.schemas import DocumentData
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import IntegrationSource


@pytest.fixture
def document_store(db_session):
    return PostgresDocumentStore(
        session=db_session, tenant_id=uuid.uuid4(), source=IntegrationSource.SLACK
    )


def test_store_new_document(document_store, db_session):
    document_data = DocumentData(
        id="test_doc_1",
        content="Test document content",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"tag1", "tag2"},
    )

    document_store.store_document(document_data)

    stored_doc = (
        db_session.query(Document)
        .filter_by(
            tenant_id=document_store.tenant_id,
            source=document_store.source,
            document_id=document_data.id,
        )
        .first()
    )

    assert stored_doc is not None
    assert stored_doc.content == document_data.content
    assert stored_doc.source_timestamp == document_data.source_timestamp
    assert set(stored_doc.tags) == document_data.tags


def test_store_existing_document(document_store, db_session):
    initial_doc = DocumentData(
        id="test_doc_2",
        content="Initial content",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"initial_tag"},
    )
    document_store.store_document(initial_doc)

    updated_doc = DocumentData(
        id="test_doc_2",
        content="Updated content",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"updated_tag"},
    )
    document_store.store_document(updated_doc)

    stored_doc = (
        db_session.query(Document)
        .filter_by(
            tenant_id=document_store.tenant_id,
            source=document_store.source,
            document_id=updated_doc.id,
        )
        .first()
    )

    assert stored_doc is not None
    assert stored_doc.content == updated_doc.content
    assert stored_doc.source_timestamp == updated_doc.source_timestamp
    assert set(stored_doc.tags) == updated_doc.tags


def test_store_document_with_embedding(document_store, db_session):
    document_data = DocumentData(
        id="test_doc_embedding",
        content="Test document with embedding",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"embedding_test"},
    )
    embedding = [0.1] * 768

    document_store.store_document(document_data, embedding)

    stored_doc = (
        db_session.query(Document)
        .filter_by(
            tenant_id=document_store.tenant_id,
            source=document_store.source,
            document_id=document_data.id,
        )
        .first()
    )

    assert stored_doc is not None
    assert stored_doc.content == document_data.content
    assert np.allclose(stored_doc.embedding, embedding)


def test_store_existing_document_with_embedding(document_store, db_session):
    document_data = DocumentData(
        id="test_doc_embedding_update",
        content="Initial content with embedding",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"embedding_test"},
    )
    initial_embedding = [0.1] * 768
    document_store.store_document(document_data, initial_embedding)

    updated_document = DocumentData(
        id="test_doc_embedding_update",
        content="Updated content with embedding",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"embedding_updated"},
    )
    updated_embedding = [0.6] * 768

    document_store.store_document(updated_document, updated_embedding)

    stored_doc = (
        db_session.query(Document)
        .filter_by(
            tenant_id=document_store.tenant_id,
            source=document_store.source,
            document_id=updated_document.id,
        )
        .first()
    )

    assert stored_doc is not None
    assert stored_doc.content == updated_document.content
    assert np.allclose(stored_doc.embedding, updated_embedding)


def test_get_document(
    document_store,
):
    original_doc = DocumentData(
        id="test_doc_3",
        content="Retrievable content",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"retrieve_tag"},
    )
    document_store.store_document(original_doc)

    retrieved_doc = document_store.get_document(original_doc.id)

    assert retrieved_doc is not None
    assert retrieved_doc.content == original_doc.content
    assert retrieved_doc.source_timestamp == original_doc.source_timestamp
    assert retrieved_doc.tags == original_doc.tags


def test_get_nonexistent_document(document_store):
    retrieved_doc = document_store.get_document("nonexistent_doc")
    assert retrieved_doc is None


def test_delete_document(document_store, db_session):
    doc_to_delete = DocumentData(
        id="test_doc_4",
        content="Document to delete",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"delete_tag"},
    )
    document_store.store_document(doc_to_delete)

    document_store.delete_document(doc_to_delete.id)

    stored_doc = (
        db_session.query(Document)
        .filter_by(
            tenant_id=document_store.tenant_id,
            source=document_store.source,
            document_id=doc_to_delete.id,
        )
        .first()
    )

    assert stored_doc is None


def test_find_document_ids_by_tag(document_store):
    docs = [
        DocumentData(
            id="doc1",
            content="Content 1",
            source_timestamp=datetime.datetime.now(datetime.UTC),
            tags={"tag_a", "tag_common"},
        ),
        DocumentData(
            id="doc2",
            content="Content 2",
            source_timestamp=datetime.datetime.now(datetime.UTC),
            tags={"tag_b", "tag_common"},
        ),
        DocumentData(
            id="doc3",
            content="Content 3",
            source_timestamp=datetime.datetime.now(datetime.UTC),
            tags={"tag_c", "tag_common"},
        ),
    ]

    for doc in docs:
        document_store.store_document(doc)

    found_docs = document_store.find_document_ids_by_tag("tag_common")

    assert len(found_docs) == 3
    assert set(found_docs) == {"doc1", "doc2", "doc3"}

    found_docs = document_store.find_document_ids_by_tag("tag_a")

    assert len(found_docs) == 1
    assert found_docs == {"doc1"}


def test_find_similar_documents(document_store):
    def make_dummy_embedding(values, dim=768):
        return values + [0.0] * (dim - len(values))

    # Create multiple documents with correct-size embeddings
    docs = [
        (
            DocumentData(
                id="similarity_doc1",
                content="Document with embedding 1",
                source_timestamp=datetime.datetime.now(datetime.UTC),
                tags={"similarity_test"},
            ),
            make_dummy_embedding([0.1, 0.2, 0.3, 0.4, 0.5]),
        ),
        (
            DocumentData(
                id="similarity_doc2",
                content="Document with embedding 2",
                source_timestamp=datetime.datetime.now(datetime.UTC),
                tags={"similarity_test"},
            ),
            make_dummy_embedding([0.15, 0.25, 0.35, 0.45, 0.55]),  # Most similar
        ),
        (
            DocumentData(
                id="similarity_doc3",
                content="Document with embedding 3",
                source_timestamp=datetime.datetime.now(datetime.UTC),
                tags={"similarity_test"},
            ),
            make_dummy_embedding([0.9, 0.8, 0.7, 0.6, 0.5]),
        ),
    ]

    for doc_data, embedding in docs:
        document_store.store_document(doc_data, embedding)

    query_embedding = make_dummy_embedding([0.14, 0.24, 0.34, 0.44, 0.54])

    similar_docs = document_store.find_similar_documents(query_embedding, limit=2)

    assert len(similar_docs) <= 2
    assert similar_docs[0][0].id == "similarity_doc2"


def test_find_similar_documents_with_tag_filter(document_store):
    def make_dummy_embedding(values, dim=768):
        return values + [0.0] * (dim - len(values))

    docs = [
        (
            DocumentData(
                id="similarity_doc1",
                content="Document with embedding 1",
                source_timestamp=datetime.datetime.now(datetime.UTC),
                tags={"similarity_test", "tag_a"},
            ),
            make_dummy_embedding([0.1, 0.2, 0.3, 0.4, 0.5]),
        ),
        (
            DocumentData(
                id="similarity_doc2",
                content="Document with embedding 2",
                source_timestamp=datetime.datetime.now(datetime.UTC),
                tags={"similarity_test", "tag_b"},
            ),
            make_dummy_embedding([0.15, 0.25, 0.35, 0.45, 0.55]),
        ),
        (
            DocumentData(
                id="similarity_doc3",
                content="Document with embedding 3",
                source_timestamp=datetime.datetime.now(datetime.UTC),
                tags={"similarity_test", "tag_a"},
            ),
            make_dummy_embedding([0.9, 0.8, 0.7, 0.6, 0.5]),
        ),
    ]

    for doc_data, embedding in docs:
        document_store.store_document(doc_data, embedding)

    query_embedding = make_dummy_embedding([0.1, 0.2, 0.3, 0.4, 0.5])

    tag_filtered_docs = document_store.find_similar_documents(
        query_embedding, limit=3, tag_filter="tag_a"
    )

    assert len(tag_filtered_docs) == 2
    assert {doc[0].id for doc in tag_filtered_docs} == {
        "similarity_doc1",
        "similarity_doc3",
    }

    tag_filtered_docs = document_store.find_similar_documents(
        query_embedding, limit=3, tag_filter="tag_b"
    )

    assert len(tag_filtered_docs) == 1
    assert tag_filtered_docs[0][0].id == "similarity_doc2"

    tag_filtered_docs = document_store.find_similar_documents(
        query_embedding, limit=3, tag_filter="nonexistent_tag"
    )

    assert len(tag_filtered_docs) == 0


def test_data_isolation_by_tenant_id(db_session):
    tenant_a = uuid.uuid4()
    tenant_b = uuid.uuid4()
    source = IntegrationSource.SLACK

    store_a = PostgresDocumentStore(
        session=db_session, tenant_id=tenant_a, source=source
    )
    store_b = PostgresDocumentStore(
        session=db_session, tenant_id=tenant_b, source=source
    )

    doc = DocumentData(
        id="iso_doc",
        content="tenant scope",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"a"},
    )
    store_a.store_document(doc)

    assert store_b.get_document("iso_doc") is None


def test_data_isolation_by_source(db_session):
    tenant_id = uuid.uuid4()
    store_slack = PostgresDocumentStore(
        session=db_session, tenant_id=tenant_id, source=IntegrationSource.SLACK
    )
    store_teams = PostgresDocumentStore(
        session=db_session, tenant_id=tenant_id, source=IntegrationSource.TEAMS
    )

    doc = DocumentData(
        id="iso_doc_src",
        content="source scope",
        source_timestamp=datetime.datetime.now(datetime.UTC),
        tags={"x"},
    )
    store_slack.store_document(doc)

    assert store_teams.get_document("iso_doc_src") is None
