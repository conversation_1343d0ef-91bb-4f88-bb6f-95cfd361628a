import uuid

import pytest

from app.integrations.models.changelog_cursor import ChangelogCursor
from app.integrations.stores.pg_cursor_store import PostgresCursorStore


@pytest.fixture
def cursor_store(db_session):
    return PostgresCursorStore(
        tenant_id=uuid.uuid4(),
        session=db_session,
    )


def test_get_position_new_cursor(cursor_store):
    position = cursor_store.get_position("new_cursor_id")
    assert position == 0


def test_update_position_new_cursor(cursor_store):
    new_position = 42
    cursor_id = "test_cursor"

    cursor_store.update_position(cursor_id, new_position)

    cursor = (
        cursor_store._session.query(ChangelogCursor)
        .filter_by(
            cursor_id=cursor_id,
            tenant_id=cursor_store._tenant_id,
        )
        .first()
    )

    assert cursor is not None
    assert cursor.cursor_position == new_position
    assert cursor.cursor_id == cursor_id
    assert cursor.tenant_id == cursor_store._tenant_id


def test_update_position_existing_cursor(cursor_store):
    initial_position = 10
    new_position = 42
    cursor_id = "test_cursor"

    initial_cursor = ChangelogCursor(
        cursor_id=cursor_id,
        tenant_id=cursor_store._tenant_id,
        cursor_position=initial_position,
    )
    cursor_store._session.add(initial_cursor)
    cursor_store._session.commit()

    cursor_store.update_position(cursor_id, new_position)

    cursor = (
        cursor_store._session.query(ChangelogCursor)
        .filter_by(
            cursor_id=cursor_id,
            tenant_id=cursor_store._tenant_id,
        )
        .first()
    )

    assert cursor is not None
    assert cursor.cursor_position == new_position
