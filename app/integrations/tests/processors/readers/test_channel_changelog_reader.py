import datetime

import pytest

from app.integrations.processors.readers.channel_changelog_reader import (
    ChannelChangelogReader,
    MessageChangeSet,
)
from app.integrations.schemas import Message<PERSON>hangelogData


@pytest.fixture
def reader(mocker):
    message_store = mocker.Mock()
    cursor_store = mocker.Mock()
    return ChannelChangelogReader(
        message_store=message_store, cursor_store=cursor_store
    )


def test_get_next_changes_returns_changeset(reader):
    mock_changes = [
        MessageChangelogData(
            cursor_id=1,
            message_id="msg1",
            operation="INSERT",
            channel_id="test_channel",
            created_at=datetime.datetime.now(),
        )
    ]
    reader._cursor_store.get_position.return_value = 0
    reader._store.get_channel_changelog.return_value = mock_changes

    changeset = reader.get_next_changes(
        channel_id="test_channel", consumer_id="test_consumer", batch_size=100
    )

    assert isinstance(changeset, MessageChangeSet)
    assert changeset.channel_id == "test_channel"
    assert changeset.consumer_id == "test_consumer"
    assert changeset.changes == mock_changes


def test_ack_updates_cursor_position(reader):
    changes = [
        MessageChangelogData(
            cursor_id=42,
            message_id="msg1",
            operation="UPDATE",
            channel_id="test_channel",
            created_at=datetime.datetime.now(),
        )
    ]
    changeset = MessageChangeSet(
        channel_id="test_channel", consumer_id="test_consumer", changes=changes
    )

    reader.ack(changeset)

    reader._cursor_store.update_position.assert_called_once_with(
        "test_consumer:test_channel", 42
    )


def test_ack_noop_on_empty_changes(reader):
    empty_changeset = MessageChangeSet(
        channel_id="test_channel", consumer_id="test_consumer", changes=[]
    )

    reader.ack(empty_changeset)

    reader._cursor_store.update_position.assert_not_called()
