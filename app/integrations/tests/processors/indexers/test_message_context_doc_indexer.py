from datetime import datetime

import pytest

from app.integrations.models import MessageChangelog
from app.integrations.processors.indexers.message_context_doc_indexer import (
    DocumentChangeSet,
    MessageWithContextToDocumentIndexer,
)
from app.integrations.schemas import MessageChangelogData, MessageData


@pytest.fixture
def mock_dependencies(mocker):
    mock_message_store = mocker.Mock()
    mock_document_store = mocker.Mock()
    mock_document_formatter = mocker.Mock()
    mock_embedder = mocker.Mock()

    mock_embedder.embed_text.return_value = [0.1, 0.2, 0.3]

    indexer = MessageWithContextToDocumentIndexer(
        message_store=mock_message_store,
        document_store=mock_document_store,
        document_formatter=mock_document_formatter,
        embedder=mock_embedder,
    )

    return {
        "indexer": indexer,
        "message_store": mock_message_store,
        "document_store": mock_document_store,
        "document_formatter": mock_document_formatter,
        "embedder": mock_embedder,
    }


def test_generate_document_no_replies(mock_dependencies):
    indexer = mock_dependencies["indexer"]
    message_store = mock_dependencies["message_store"]
    document_formatter = mock_dependencies["document_formatter"]
    document_store = mock_dependencies["document_store"]
    embedder = mock_dependencies["embedder"]

    message_id = "test_message_id"
    channel_id = "test_channel_id"
    sent_at = datetime.now()
    main_message = MessageData(
        message_id=message_id,
        channel_id=channel_id,
        content="Test message",
        sent_at=sent_at,
        thread_id=message_id,
    )

    message_store.get_message.return_value = main_message
    message_store.get_thread_replies.return_value = []
    document_formatter.format_document_content.return_value = "Formatted content"

    document = indexer.generate_document(message_id)

    message_store.get_message.assert_called_once_with(message_id)
    message_store.get_thread_replies.assert_called_once_with(
        channel_id=channel_id, message_id=message_id
    )
    document_formatter.format_document_content.assert_called_once_with(
        main_message=main_message, context_messages=None, replies=[]
    )

    embedder.embed_text.assert_called_once_with("Formatted content")

    document_store.store_document.assert_called_once_with(document, [0.1, 0.2, 0.3])

    assert document.id == message_id
    assert document.content == "Formatted content"
    assert document.source_timestamp == sent_at
    assert document.tags == {f"channel_id:{channel_id}"}, (
        "Tags should contain channel_id when no replies or context messages"
    )


def test_generate_document_last_edit_at_as_source_timestamp(mock_dependencies):
    indexer = mock_dependencies["indexer"]
    message_store = mock_dependencies["message_store"]
    document_formatter = mock_dependencies["document_formatter"]
    document_store = mock_dependencies["document_store"]

    message_id = "test_message_id"
    channel_id = "test_channel_id"
    sent_at = datetime(2025, 1, 1)
    last_edit_at = datetime(2025, 1, 2)
    main_message = MessageData(
        message_id=message_id,
        channel_id=channel_id,
        content="Test message",
        sent_at=sent_at,
        last_edit_at=last_edit_at,
        thread_id=None,
    )

    message_store.get_message.return_value = main_message
    message_store.get_main_messages_before.return_value = []

    document_formatter.format_document_content.return_value = "Formatted content"

    document = indexer.generate_document(message_id)

    assert document.id == message_id
    assert document.source_timestamp == last_edit_at
    document_store.store_document.assert_called_once_with(document, [0.1, 0.2, 0.3])


def test_generate_document_with_replies(mock_dependencies):
    indexer = mock_dependencies["indexer"]
    message_store = mock_dependencies["message_store"]
    document_formatter = mock_dependencies["document_formatter"]
    document_store = mock_dependencies["document_store"]
    embedder = mock_dependencies["embedder"]

    message_id = "test_message_id"
    channel_id = "test_channel_id"
    main_message = MessageData(
        message_id=message_id,
        channel_id=channel_id,
        content="Test message",
        sent_at=datetime.now(),
        thread_id=message_id,
    )

    replies = [
        MessageData(
            message_id="reply1",
            channel_id=channel_id,
            content="Reply 1",
            sent_at=datetime.now(),
            thread_id=message_id,
        ),
        MessageData(
            message_id="reply2",
            channel_id=channel_id,
            content="Reply 2",
            sent_at=datetime.now(),
            thread_id=message_id,
        ),
    ]

    message_store.get_message.return_value = main_message
    message_store.get_thread_replies.return_value = replies
    document_formatter.format_document_content.return_value = (
        "Formatted content with replies"
    )

    document = indexer.generate_document(message_id)

    message_store.get_message.assert_called_once_with(message_id)
    message_store.get_thread_replies.assert_called_once_with(
        channel_id=channel_id, message_id=message_id
    )
    document_formatter.format_document_content.assert_called_once_with(
        main_message=main_message, context_messages=None, replies=replies
    )

    embedder.embed_text.assert_called_once_with("Formatted content with replies")

    document_store.store_document.assert_called_once_with(document, [0.1, 0.2, 0.3])

    assert document.id == message_id
    assert document.content == "Formatted content with replies"
    assert document.tags == {f"channel_id:{channel_id}", "reply1", "reply2"}, (
        "Tags should contain channel_id and reply message IDs"
    )


def test_generate_document_with_context_messages(mock_dependencies):
    indexer = mock_dependencies["indexer"]
    message_store = mock_dependencies["message_store"]
    document_formatter = mock_dependencies["document_formatter"]
    document_store = mock_dependencies["document_store"]
    embedder = mock_dependencies["embedder"]

    message_id = "test_message_id"
    channel_id = "test_channel_id"
    main_message = MessageData(
        message_id=message_id,
        channel_id=channel_id,
        content="Test message",
        sent_at=datetime.now(),
    )

    context_messages = [
        MessageData(
            message_id="context1",
            channel_id=channel_id,
            content="Context Message 1",
            sent_at=datetime.now(),
        ),
        MessageData(
            message_id="context2",
            channel_id=channel_id,
            content="Context Message 2",
            sent_at=datetime.now(),
        ),
    ]

    message_store.get_message.return_value = main_message
    message_store.get_main_messages_before.return_value = context_messages
    document_formatter.format_document_content.return_value = (
        "Formatted content with context"
    )

    document = indexer.generate_document(message_id)

    message_store.get_message.assert_called_once_with(message_id)
    message_store.get_main_messages_before.assert_called_once_with(
        channel_id=channel_id, message_id=message_id, limit=10
    )
    document_formatter.format_document_content.assert_called_once_with(
        main_message=main_message, context_messages=context_messages, replies=None
    )

    embedder.embed_text.assert_called_once_with("Formatted content with context")

    document_store.store_document.assert_called_once_with(document, [0.1, 0.2, 0.3])

    assert document.id == message_id
    assert document.content == "Formatted content with context"
    assert document.tags == {f"channel_id:{channel_id}", "context1", "context2"}, (
        "Tags should contain channel_id and context message IDs"
    )


def test_generate_document_do_nothing(mock_dependencies):
    indexer = mock_dependencies["indexer"]
    message_store = mock_dependencies["message_store"]
    embedder = mock_dependencies["embedder"]

    message_id = "test_message_id"
    reply_message = MessageData(
        message_id=message_id,
        channel_id="test_channel_id",
        content="Test message",
        sent_at=datetime.now(),
        thread_id="other_thread_id",
    )

    message_store.get_message.return_value = reply_message

    document = indexer.generate_document(message_id)

    embedder.embed_text.assert_not_called()

    assert document is None


def test_embedding_error_handling(mock_dependencies):
    indexer = mock_dependencies["indexer"]
    message_store = mock_dependencies["message_store"]
    document_formatter = mock_dependencies["document_formatter"]
    document_store = mock_dependencies["document_store"]
    embedder = mock_dependencies["embedder"]

    embedder.embed_text.side_effect = Exception("Embedding failed")

    message_id = "test_message_id"
    channel_id = "test_channel_id"
    main_message = MessageData(
        message_id=message_id,
        channel_id=channel_id,
        content="Test message",
        sent_at=datetime.now(),
    )

    message_store.get_message.return_value = main_message
    message_store.get_main_messages_before.return_value = []
    document_formatter.format_document_content.return_value = "Formatted content"

    document = indexer.generate_document(message_id)

    assert document.id == message_id
    assert document.content == "Formatted content"

    document_store.store_document.assert_called_once_with(document, None)


def test_clean_changelog_operations(mock_dependencies):
    indexer = mock_dependencies["indexer"]

    channel_id = "test_channel"

    # Scenario 1: INSERT followed by DELETE
    changes1 = [
        MessageChangelogData(
            channel_id=channel_id,
            operation=MessageChangelog.Operation.INSERT,
            message_id="msg1",
            cursor_id=1,
            created_at=datetime.now(),
        ),
        MessageChangelogData(
            channel_id=channel_id,
            operation=MessageChangelog.Operation.DELETE,
            message_id="msg1",
            cursor_id=2,
            created_at=datetime.now(),
        ),
    ]

    cleaned_ops1 = indexer._clean_changelog_operations(changes1)
    assert len(cleaned_ops1) == 0

    # Scenario 2: Non existing op
    changes2 = [
        MessageChangelogData(
            channel_id=channel_id,
            operation=MessageChangelog.Operation.DELETE,
            message_id="msg2",
            cursor_id=1,
            created_at=datetime.now(),
        )
    ]

    cleaned_ops2 = indexer._clean_changelog_operations(changes2)
    assert len(cleaned_ops2) == 1
    assert list(cleaned_ops2.values())[0].operation == MessageChangelog.Operation.DELETE

    # Scenario 3: Multiple operations with priority
    changes3 = [
        MessageChangelogData(
            channel_id=channel_id,
            operation=MessageChangelog.Operation.UPDATE,
            message_id="msg3",
            cursor_id=1,
            created_at=datetime.now(),
        ),
        MessageChangelogData(
            channel_id=channel_id,
            operation=MessageChangelog.Operation.INSERT,
            message_id="msg4",
            cursor_id=2,
            created_at=datetime.now(),
        ),
        MessageChangelogData(
            channel_id=channel_id,
            operation=MessageChangelog.Operation.DELETE,
            message_id="msg3",
            cursor_id=3,
            created_at=datetime.now(),
        ),
        MessageChangelogData(
            channel_id=channel_id,
            operation=MessageChangelog.Operation.UPDATE,
            message_id="msg4",
            cursor_id=4,
            created_at=datetime.now(),
        ),
    ]

    cleaned_ops3 = indexer._clean_changelog_operations(changes3)
    assert len(cleaned_ops3) == 2
    assert list(cleaned_ops3.values())[0].operation == MessageChangelog.Operation.DELETE
    assert list(cleaned_ops3.values())[1].operation == MessageChangelog.Operation.INSERT


def test_determine_document_changes(mock_dependencies):
    indexer = mock_dependencies["indexer"]
    document_store = mock_dependencies["document_store"]

    channel_id = "test_channel"

    changes = [
        MessageChangelogData(
            channel_id=channel_id,
            operation=MessageChangelog.Operation.DELETE,
            message_id="msg1",
            cursor_id=1,
            created_at=datetime.now(),
        )
    ]

    document_store.find_document_ids_by_tag.return_value = {
        "related_doc1",
        "related_doc2",
    }

    rebuild_context = indexer.determine_document_changes(changes)

    assert isinstance(rebuild_context, DocumentChangeSet)
    assert rebuild_context.documents_to_invalidate == {"msg1"}
    assert rebuild_context.documents_to_rebuild == {
        "related_doc1",
        "related_doc2",
    }
    document_store.find_document_ids_by_tag.assert_called_once_with("msg1")


def test_find_document_ids_to_rebuild(mock_dependencies):
    indexer = mock_dependencies["indexer"]
    document_store = mock_dependencies["document_store"]

    document_store.find_document_ids_by_tag.return_value = {"doc1", "doc2"}

    result = indexer._find_document_ids_to_rebuild("test_message_id")

    assert result == {"doc1", "doc2"}
    document_store.find_document_ids_by_tag.assert_called_once_with("test_message_id")


def test_invalidate_document(mock_dependencies):
    indexer = mock_dependencies["indexer"]
    document_store = mock_dependencies["document_store"]

    indexer.invalidate_document("test_message_id")

    document_store.delete_document.assert_called_once_with("test_message_id")
