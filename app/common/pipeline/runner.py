import threading
import time

from app.common.helpers.logger import get_logger
from app.common.pipeline.base_stage import BaseStage
from app.common.pipeline.sequential_stage import SequentialStage


class PipelineRunner:
    """
    Runner for pipeline stages with daemon support and controlled stopping.
    """

    def __init__(self, stages: list[BaseStage] | None = None):
        """Initialize the pipeline runner."""
        self.stages = stages or []
        self.logger = get_logger(self.__class__.__name__)

        # Thread management
        self._threads: dict[str, threading.Thread] = {}
        self.is_running = False

        # Daemon control
        self.should_stop = False

    def add_stage(self, stage: BaseStage) -> None:
        """
        Add a stage to the pipeline.

        Args:
            stage: The stage to add

        Raises:
            RuntimeError: If the pipeline is already running
        """
        if self.is_running:
            raise RuntimeError("Cannot add stages while the pipeline is running")

        self.stages.append(stage)
        self.logger.info(f"Added stage: {stage.__class__.__name__}")

    def run(self) -> dict:
        """
        Execute all stages once.

        Returns:
            Results of the execution
        """
        if len(self.stages) == 1:
            # Single stage - execute directly
            return self.stages[0].execute_once()
        elif len(self.stages) > 1:
            # Multiple stages - create a sequential stage
            sequential = SequentialStage(self.stages)
            return sequential.execute_once()
        else:
            self.logger.warning("No stages to execute")
            return {}

    def start_daemon(
        self, sequential_execution: bool = False, sequential_interval_seconds: int = 60
    ) -> None:
        """
        Start the pipeline in daemon mode.

        Args:
            sequential_execution: Whether to run stages sequentially
            sequential_interval_seconds: Interval between sequential executions

        Raises:
            RuntimeError: If the pipeline is already running
        """
        # Check if already running
        if self.is_running:
            raise RuntimeError("Pipeline is already running")

        # Reset daemon control flags
        self.should_stop = False
        self.is_running = True

        if sequential_execution and len(self.stages) > 1:
            # Sequential mode - create a composite stage
            sequential_stage = SequentialStage(
                self.stages, interval_seconds=sequential_interval_seconds
            )
            self._start_stage_thread(sequential_stage, "sequential")

        else:
            # Parallel mode - start each stage in its own thread
            for stage in self.stages:
                stage_name = stage.__class__.__name__
                self._start_stage_thread(stage, stage_name)

        # Keep the process alive until interrupted
        try:
            self.logger.info(
                f"Pipeline daemon running in {'sequential' if sequential_execution else 'parallel'} mode"
            )
            while not self.should_stop:
                # Check if any thread has stopped
                if any(not thread.is_alive() for thread in self._threads.values()):
                    self.logger.warning("A thread has stopped unexpectedly")
                    break

                time.sleep(1)
        except (KeyboardInterrupt, SystemExit):
            self.logger.info("Interruption detected, stopping pipeline...")

        self.stop_daemon()

    def _start_stage_thread(self, stage: BaseStage, name: str):
        """Start a daemon thread for a stage."""
        thread = threading.Thread(target=stage.execute, name=name, daemon=True)
        thread.start()
        self._threads[name] = thread

        self.logger.info(f"Started thread for {name}")

    def stop_daemon(self) -> None:
        """Stop all daemon threads."""
        if not self.is_running:
            self.logger.info("Pipeline is not running")
            return

        # Set stop flags
        self.should_stop = True
        self.is_running = False

        self.logger.info("Stopping pipeline...")

        # Signal all stages to stop
        for stage in self.stages:
            stage.stop()

        # Wait for threads to terminate
        timeout_per_stage = 10
        for name, thread in list(self._threads.items()):
            if thread.is_alive():
                self.logger.info(f"Waiting for {name} to stop...")
                thread.join(timeout=timeout_per_stage)
                if thread.is_alive():
                    self.logger.warning(f"Thread {name} did not stop within timeout")
                else:
                    self.logger.info(f"Thread {name} stopped")

        # Clean up
        self._threads = {}

        self.logger.info("Pipeline stopped")

    def get_status(self) -> dict:
        """Get the current pipeline status."""
        return {
            "running": self.is_running,
            "should_stop": self.should_stop,
            "stages": [
                {
                    "name": stage.__class__.__name__,
                    "running": stage.__class__.__name__ in self._threads
                    and self._threads[stage.__class__.__name__].is_alive(),
                    "should_stop": stage.should_stop,
                }
                for stage in self.stages
            ],
        }
