import threading

from app.common.pipeline.base_stage import BaseStage
from app.common.pipeline.runner import PipelineRunner


class MockStage(BaseStage):
    def __init__(self, name):
        super().__init__()
        self.name = name
        self.executed = False

    def execute_once(self):
        self.executed = True
        return f"Result from {self.name}"

    def execute(self):
        self.execute_once()
        while not self.should_stop:
            threading.Event().wait(0.1)


def test_pipeline_runner_add_stage():
    runner = PipelineRunner()
    stage = MockStage("Test Stage")
    runner.add_stage(stage)

    assert len(runner.stages) == 1
    assert runner.stages[0] == stage


def test_pipeline_runner_run_single_stage():
    stage = MockStage("Test Stage")
    runner = PipelineRunner([stage])

    result = runner.run()
    assert result == "Result from Test Stage"
    assert stage.executed


def test_pipeline_runner_start_daemon_parallel(mocker):
    stage1 = MockStage("Stage1")
    stage2 = MockStage("Stage2")
    runner = <PERSON>pelineRunner([stage1, stage2])

    mocker.patch("time.sleep", return_value=None)

    daemon_thread = threading.Thread(target=runner.start_daemon)
    daemon_thread.start()

    threading.Event().wait(0.05)

    assert not stage1.should_stop
    assert not stage2.should_stop

    runner.should_stop = True

    daemon_thread.join(timeout=1)

    assert not runner.is_running
    assert stage1.should_stop
    assert stage2.should_stop
    assert runner._threads == {}

    status = runner.get_status()
    assert status["running"] is False
    for stage_status in status["stages"]:
        assert stage_status["running"] is False


def test_pipeline_runner_stop_daemon(mocker):
    stage1 = MockStage("Stage 1")
    stage2 = MockStage("Stage 2")

    runner = PipelineRunner([stage1, stage2])

    mocker.patch("time.sleep", return_value=None)

    daemon_thread = threading.Thread(target=runner.start_daemon)
    daemon_thread.start()

    threading.Event().wait(0.05)

    runner.stop_daemon()

    daemon_thread.join(timeout=2)

    assert not runner.is_running
    assert runner.should_stop
    assert not daemon_thread.is_alive()
