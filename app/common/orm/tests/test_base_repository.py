import pytest
from sqlalchemy import Column, String
from sqlalchemy.orm import Session

from app.common.orm.base_repository import BaseRepository
from app.core.database import BaseModel


class DummyModel(BaseModel):
    __tablename__ = "dummy_model"

    name = Column(String, index=True)
    city = Column(String, index=True)


@pytest.fixture(autouse=True)
def add_dummy_model(db_session: Session):
    assert (bind := db_session.bind) is not None
    dummy_table = DummyModel.metadata.tables[DummyModel.__tablename__]
    DummyModel.metadata.create_all(bind, tables=[dummy_table])


@pytest.fixture
def repository(db_session: Session) -> BaseRepository[DummyModel]:
    return BaseRepository(db_session, DummyModel)


@pytest.fixture
def inserted_records(db_session: Session) -> tuple[DummyModel, DummyModel]:
    record_1 = DummyModel(name="record_1", city="Paris")
    record_2 = DummyModel(name="record_2", city="Paris")
    db_session.add_all([record_1, record_2])
    db_session.commit()
    return (record_1, record_2)


def test_get_all(
    repository: BaseRepository[DummyModel],
    inserted_records: tuple[DummyModel, DummyModel],
):
    record_1, record_2 = inserted_records

    records = repository.get_all()
    assert len(records) == 2
    assert {record_1.name, record_2.name} == {record.name for record in records}


def test_get_by_id(
    repository: BaseRepository[DummyModel],
    inserted_records: tuple[DummyModel, DummyModel],
):
    record_1, _ = inserted_records

    record = repository.get_by_id(record_1.id)
    assert record is not None
    assert record.id == record_1.id
    assert record.name == record_1.name


def test_get_by_attr(
    repository: BaseRepository[DummyModel],
    inserted_records: tuple[DummyModel, DummyModel],
):
    _ = inserted_records
    recors = repository._get_by_attrs(city="Paris")
    assert len(recors) == 2


def test_get_by_attrs__attr_doesnt_exist(
    repository: BaseRepository[DummyModel],
    inserted_records: tuple[DummyModel, DummyModel],
):
    _ = inserted_records
    with pytest.raises(AttributeError):
        repository._get_by_attrs(attr_that_doesnt_exist=1)


def test_create(
    db_session: Session,
    repository: BaseRepository[DummyModel],
):
    record = repository.create(
        name="record_1",
        city="Paris",
    )

    assert record.id is not None
    record_id = record.id

    db_session.commit()

    record_from_db = db_session.query(DummyModel).filter_by(id=record_id).first()
    assert record_from_db is not None
    assert record_from_db.id == record_id
    assert record_from_db.name == "record_1"


def test_create__attr_doesnt_exist(
    repository: BaseRepository[DummyModel],
):
    with pytest.raises(AttributeError):
        repository.create(attr_that_doesnt_exist=1)


def test_update(
    db_session: Session,
    repository: BaseRepository[DummyModel],
    inserted_records: tuple[DummyModel, DummyModel],
):
    record_1, _ = inserted_records

    repository.update(record_1.id, name="updated_name")

    assert record_1.name == "updated_name"

    db_session.commit()

    record_from_db = db_session.query(DummyModel).filter_by(id=record_1.id).first()
    assert record_from_db is not None
    assert record_from_db.id == record_1.id
    assert record_from_db.name == "updated_name"


def test_update__attr_doesnt_exist(
    repository: BaseRepository[DummyModel],
    inserted_records: tuple[DummyModel, DummyModel],
):
    record_1, _ = inserted_records

    with pytest.raises(AttributeError):
        repository.update(record_1.id, attr_that_doesnt_exist=1)


def test_delete(
    db_session: Session,
    repository: BaseRepository[DummyModel],
    inserted_records: tuple[DummyModel, DummyModel],
):
    record_1, _ = inserted_records

    repository.delete(record_1.id)

    db_session.commit()

    record_from_db = db_session.query(DummyModel).filter_by(id=record_1.id).first()
    assert record_from_db is None
